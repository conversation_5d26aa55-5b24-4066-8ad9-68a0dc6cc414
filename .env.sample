### Optional ###
# Server Base Setting
APP_PORT=3000 # 起後端的 Port
APP_ADDRESS="0.0.0.0"
APP_GLOBAL_PREFIX="api" # 全域 Path 的前綴，此範例表示 ${domain}/api
APP_CORS_ALLOW_ORIGIN=*  # Access-Control-Allow-Origin

# Swagger Setting
SWAGGER_DOCUMENT_TITLE="Swagger Document Title" # Swagger 文件標題
SWAGGER_DOCUMENT_DESCRIPTION="Swagger Document Description" # Swagger 文件描述
SWAGGER_DOCUMENT_VERSION="Swagger Document Version" # Swagger 文件版本
SWAGGER_DOCUMENT_PATH="/api/docs" # Swagger 文件路徑

### Required ###
# Server Base Setting
APP_ENV='dev' # e.g. dev, test, stg, prod

# Frontend Team DB Setting
DB_TYPE='postgres'  # Frontend DB 類型
DB_HOST='localhost' # Frontend DB Host (private)
DB_PORT=5432 # Frontend DB Port (private)
DB_USER='postgres' # Frontend DB 使用者 (private)
DB_PASSWORD='admin' # Frontend DB 密碼 (private)
DB_NAME='name' # Frontend DB 名稱 (private)

# OKTA Login Setting
OKTA_ISSUER="https://..." # Okta 登入的 issuer (private)
OKTA_CLIENT_ID="..." # Okta 登入的 client id (private)

# User Auth Setting
USER_AUTH_SECRET="secret" # 加密 JWT Token 的 Secret (private, please random)
USER_AUTH_EXPIRED_TIME="7d" # 使用者登入後核發的 JWT Token 時效

# Unleash Feature Flag Setting
UNLEASH_API_URL="" # (private)
UNLEASH_API_TOKEN="" # (private)
UNLEASH_APP_NAME="" # (private)
UNLEASH_REFRESH_INTERVAL=60000

# SendGrid and Mail Setting
SEND_MAIL_LOGO_URL="https://..."
SEND_MAIL_API_KEY="key" # (private)
SEND_MAIL_SENDER_EMAIL="<EMAIL>"
SEND_MAIL_SET_PASSWORD_PAGE_URL="http://localhost:8080"
SEND_MAIL_SET_PASSWORD_TOKEN_EXPIRES_IN="14d"
SEND_MAIL_SET_PASSWORD_TOKEN_SECRET="secret" # (private, please random)
SEND_MAIL_RESET_PASSWORD_PAGE_URL="http://localhost:8080/resetPassword"
SEND_MAIL_RESET_PASSWORD_TOKEN_EXPIRES_IN="1h"
SEND_MAIL_RESET_PASSWORD_TOKEN_SECRET="secret" # (private, please random)

# Shared API Setting
BACKEND_API_URL="https://...." # Backend RESTful API 的 URL
BACKEND_API_TOKEN=""