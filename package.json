{"name": "<PERSON><PERSON>n-nest<PERSON>s-backstage-template", "version": "0.0.1", "description": "Node Version: v20.18.2 (npm v10.8.2)", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "import:permission-data": "node dist/commands/console.js import:permission-data", "import:operation-data": "node dist/commands/console.js import:operation-data", "import:admin-role": "node dist/commands/console.js import:admin-role", "import:admin-user": "node dist/commands/console.js import:admin-user", "prepare": "husky install"}, "dependencies": {"@fastify/static": "^7.0.4", "@nestjs/axios": "^3.1.3", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-fastify": "^10.4.15", "@nestjs/swagger": "^8.1.0", "@nestjs/typeorm": "^10.0.2", "@okta/jwt-verifier": "^4.0.1", "@sendgrid/mail": "^8.1.4", "axios": "^1.7.9", "bcrypt": "^5.1.1", "casbin": "^5.36.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "handlebars": "^4.7.8", "nestjs-console": "^9.0.0", "passport-jwt": "^4.0.1", "pg": "^8.13.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "typeorm": "^0.3.20", "typeorm-adapter": "^1.7.0", "unleash-client": "^6.3.1"}, "devDependencies": {"@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^9.1.7", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}