import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { AppUsersService } from './app-users.service';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiOkResponse,
} from '@nestjs/swagger';
import UserAuthGuard from '../auth/guards/user-auth.guard';
import { AppUsersExceptionFilter } from './filter/app-users-exception.filter';
import { AppUsersResponseFilter } from './filter/app-users-response.filter';
import {
  GetAppUserListByOpenIdsResult,
  GetAppUserListByOpenIdsResultRo,
} from './entities/get-app-user-list-by-open-ids.entity';
import { GetAppUserListByOpenIdsDto } from './dto/get-app-user-list-by-open-ids.dto';
import { AppUsersResponseFilterTypeEnum } from './filter/app-users.filter.interface';
import { customApiOperationDescription } from '../../utils/swagger';

@ApiBearerAuth()
@UseGuards(UserAuthGuard)
@Controller('app-users')
export class AppUsersController {
  constructor(
    private readonly appUsersService: AppUsersService,
    private readonly appUsersExceptionFilter: AppUsersExceptionFilter,
    private readonly appUsersResponseFilter: AppUsersResponseFilter,
  ) {}

  @ApiTags('粉絲團管理 > 粉絲團列表')
  @Get('/open-ids')
  @ApiOperation({
    summary: '透過 OpenIDs 取得 APP 使用者列表',
    description: customApiOperationDescription({
      relatedBEApis: ['[GET] /admin/v1/users'],
    }),
  })
  @ApiOkResponse({ type: GetAppUserListByOpenIdsResultRo })
  async getAppUserListByOpenIds(
    @Query() getAppUserListByOpenIdsDto: GetAppUserListByOpenIdsDto,
  ): Promise<GetAppUserListByOpenIdsResult> {
    try {
      const res = await this.appUsersService.getAppUserListByOpenIds(
        getAppUserListByOpenIdsDto,
      );
      const result = this.appUsersResponseFilter.getResponse({
        type: AppUsersResponseFilterTypeEnum.GET_APP_USER_LIST_BY_OPEN_IDS,
        payload: res,
      }) as GetAppUserListByOpenIdsResult;
      return result;
    } catch (error) {
      throw this.appUsersExceptionFilter.getResponse(error);
    }
  }
}
