import { Module } from '@nestjs/common';
import { AppUsersService } from './app-users.service';
import { AppUsersController } from './app-users.controller';
import { AppUsersExceptionFilter } from './filter/app-users-exception.filter';
import { AppUsersResponseFilter } from './filter/app-users-response.filter';
import { OperatorUsersModuleByBackend } from '../../shared/restful/backend/operator/users/users.module';

@Module({
  imports: [OperatorUsersModuleByBackend],
  controllers: [AppUsersController],
  providers: [AppUsersService, AppUsersExceptionFilter, AppUsersResponseFilter],
})
export class AppUsersModule {}
