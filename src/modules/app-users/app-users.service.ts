import { Injectable } from '@nestjs/common';
import { OperatorUsersServiceByBackend } from '../../shared/restful/backend/operator/users/users.service';
import {
  GetAppUserListByOpenIdsReq,
  GetAppUserListByOpenIdsRes,
} from './app-users.interface';

@Injectable()
export class AppUsersService {
  constructor(
    private readonly operatorUsersServiceByBackend: OperatorUsersServiceByBackend,
  ) {}

  async getAppUserListByOpenIds({
    openIds,
  }: GetAppUserListByOpenIdsReq): Promise<GetAppUserListByOpenIdsRes> {
    const { users: appUsers } =
      await this.operatorUsersServiceByBackend.getUserListByOpenIds({
        openIds,
      });

    return {
      users:
        appUsers?.map((appUser) => ({
          id: appUser.id,
          name: appUser.name,
          email: appUser.email,
          openId: appUser.openId,
          createdAt: appUser.createdTime,
          updatedAt: appUser.updatedTime,
        })) || [],
    };
  }
}
