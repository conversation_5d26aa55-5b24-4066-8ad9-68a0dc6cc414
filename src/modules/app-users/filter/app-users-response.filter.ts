import { Injectable } from '@nestjs/common';
import {
  GetAppUserListByOpenIdsResponseFilterPayload,
  AppUsersFilter,
  AppUsersResponseFilterOriginalResult,
  AppUsersResponseFilterResult,
  AppUsersResponseFilterTypeEnum,
} from './app-users.filter.interface';
import { GetAppUserListByOpenIdsResult } from '../entities/get-app-user-list-by-open-ids.entity';

@Injectable()
export class AppUsersResponseFilter
  implements
    AppUsersFilter<
      AppUsersResponseFilterOriginalResult,
      AppUsersResponseFilterResult
    >
{
  getResponse(
    orig: AppUsersResponseFilterOriginalResult,
  ): AppUsersResponseFilterResult {
    switch (orig.type) {
      case AppUsersResponseFilterTypeEnum.GET_APP_USER_LIST_BY_OPEN_IDS: {
        const payload =
          orig.payload as GetAppUserListByOpenIdsResponseFilterPayload;
        let result = new GetAppUserListByOpenIdsResult();
        result = {
          users: payload.users,
        };
        return result;
      }
    }
  }
}
