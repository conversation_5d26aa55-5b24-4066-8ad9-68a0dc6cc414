import { HttpException, HttpStatus } from '@nestjs/common';
import { AppUsersFilter } from './app-users.filter.interface';

export class AppUsersExceptionFilter
  implements AppUsersFilter<Error, HttpException>
{
  getResponse({ message: originalErrorMessage }: Error): HttpException {
    let errorMessage: string;
    let statusCode: number;

    switch (originalErrorMessage) {
      default:
        errorMessage = originalErrorMessage;
        statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
    }

    return new HttpException(
      {
        message: errorMessage,
      },
      statusCode,
    );
  }
}
