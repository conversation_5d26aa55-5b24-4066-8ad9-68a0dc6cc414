import { GetAppUserListByOpenIdsResult } from '../entities/get-app-user-list-by-open-ids.entity';

export enum AppUsersResponseFilterTypeEnum {
  GET_APP_USER_LIST_BY_OPEN_IDS = 'GET_APP_USER_LIST_BY_OPEN_IDS',
}

export interface AppUsersFilter<T, U> {
  getResponse(orig: T): U;
}

export interface GetAppUserListByOpenIdsResponseFilterPayload {
  users: {
    id: string;
    name: string;
    email: string;
    openId: string;
    createdAt: number;
    updatedAt: number;
  }[];
}

export interface AppUsersResponseFilterOriginalResult {
  type: AppUsersResponseFilterTypeEnum;
  payload: GetAppUserListByOpenIdsResponseFilterPayload;
}

export type AppUsersResponseFilterResult = GetAppUserListByOpenIdsResult;
