import { ApiProperty } from '@nestjs/swagger';
import { customApiPropertyDescription } from '../../../utils/swagger';
import {
  ApiResponseStatusEnum,
  ResponseInterface,
} from '../../../interceptors/response/response.interface';

class AppUserByOpenId {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'ID',
    }),
    example: 'id',
  })
  id: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '名稱',
    }),
    example: 'test',
  })
  name: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '信箱',
    }),
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'OpenID',
    }),
    example: 'openId',
  })
  openId: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '建立時間',
    }),
    example: 1693900215343,
  })
  createdAt: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '最後更新時間',
    }),
    example: 1693900215343,
  })
  updatedAt: number;
}

export class GetAppUserListByOpenIdsResult {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'APP 使用者列表',
    }),
    type: [AppUserByOpenId],
  })
  users: AppUserByOpenId[];
}

export class GetAppUserListByOpenIdsResultRo
  implements ResponseInterface<GetAppUserListByOpenIdsResult>
{
  @ApiProperty({ example: 200 })
  code: number;

  @ApiProperty({
    enum: ApiResponseStatusEnum,
    example: ApiResponseStatusEnum.SUCCESS,
  })
  status: ApiResponseStatusEnum;

  @ApiProperty({ type: GetAppUserListByOpenIdsResult })
  data: GetAppUserListByOpenIdsResult;
}
