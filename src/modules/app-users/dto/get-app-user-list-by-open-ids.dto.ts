import { ApiProperty } from '@nestjs/swagger';
import {
  ArrayMinSize,
  ArrayUnique,
  IsNotEmpty,
  IsString,
} from 'class-validator';
import { customApiPropertyDescription } from '../../../utils/swagger';
import { TransformToArray } from '../../../decorators/transformers/TransformToArray';

export class GetAppUserListByOpenIdsDto {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'APP 使用者的 OpenID',
      limitations: ['至少一個', '不可重複'],
    }),
    type: [String],
    example: ['123456789009876567'],
  })
  @TransformToArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  @ArrayMinSize(1)
  @ArrayUnique()
  readonly openIds: string[];
}
