import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString } from 'class-validator';
import { customApiPropertyDescription } from '../../../utils/swagger';

export class SignInByPasswordDto {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '信箱',
    }),
  })
  @IsEmail()
  readonly email: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '密碼',
    }),
  })
  @IsString()
  readonly password: string;
}
