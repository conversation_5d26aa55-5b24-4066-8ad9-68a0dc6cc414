import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';
import { IsNotBlank } from '../../../decorators/validations/IsNotBlank';
import { customApiPropertyDescription } from '../../../utils/swagger';

export class SignInByOktaDto {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'number used once',
      remarks: ['對應前端解析的 claims.nonce'],
    }),
    example: 'nonce',
  })
  @IsString()
  @IsNotBlank()
  readonly nonce: string;
}
