import { Injectable } from '@nestjs/common';
import * as OktaJwtVerifier from '@okta/jwt-verifier';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import {
  SignInByOktaReq,
  SignInByOktaRes,
  SignInByPasswordReq,
  SignInByPasswordRes,
  UserStatusEnum,
} from './auth.interface';
import { UnleashFeatureFlagsService } from '../unleash-feature-flags/unleash-feature-flags.service';
import { UnleashFeatureFlagNameEnum } from '../unleash-feature-flags/unleash-feature-flags.interface';
import * as bcrypt from 'bcrypt';

@Injectable()
export class AuthService {
  constructor(
    private readonly usersService: UsersService,
    private readonly unleashFeatureFlagsService: UnleashFeatureFlagsService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {
    this.jwtService = new JwtService({
      secret: configService.get('auth.user.secret'),
      signOptions: { expiresIn: configService.get('auth.user.expired_time') },
    });
  }
  async signInByOkta({
    authorization,
    nonce,
  }: SignInByOktaReq): Promise<SignInByOktaRes> {
    const oktaAuthClient = new OktaJwtVerifier({
      issuer: this.configService.get('okta.issuer'),
    });
    const { claims } = await oktaAuthClient
      .verifyIdToken(
        authorization,
        this.configService.get('okta.clientId'),
        nonce,
      )
      .catch(() => {
        throw new Error('Invalid Okta ID Token.');
      });

    const user = await this.usersService
      .getUserByEmail(claims.email as string)
      .catch((error) => {
        throw new Error(error);
      });

    if (!user || user.deleterUserId) {
      throw new Error('No account match that account.');
    }

    if (user.status === UserStatusEnum.SUSPENDED) {
      throw new Error('User status is suspended, please contact the manager.');
    }

    // MEMO: 不更新 updated_at (最後更新時間)
    await this.usersService.updateUserStatus({
      id: user.id,
      status: UserStatusEnum.VERIFIED,
      updatedAt: () => '"updated_at"',
    });

    return this.jwtService.sign({
      userId: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
    });
  }

  async signInByPassword({
    email,
    password,
  }: SignInByPasswordReq): Promise<SignInByPasswordRes> {
    // MEMO: 透過 Unleash 功能判斷是否可以使用密碼登入
    const isEnabled = await this.unleashFeatureFlagsService.isEnabled(
      UnleashFeatureFlagNameEnum.DISABLE_PASSWORD_LOGIN,
    );
    if (isEnabled) throw new Error('This feature is not available.');

    const user = await this.usersService
      .getUserByEmail(email as string)
      .catch((error) => {
        throw new Error(error);
      });

    if (!user || user.deleterUserId) {
      throw new Error('No account match that account.');
    }

    switch (user.status) {
      case UserStatusEnum.UNVERIFIED:
        throw new Error('User status is unverified, please set password.');
      case UserStatusEnum.SUSPENDED:
        throw new Error(
          'User status is suspended, please contact the manager.',
        );
    }

    if (!user.passwordLoginAllowed) {
      throw new Error('Password login is not allowed.');
    }

    const isPasswordMatch = await bcrypt.compare(password, user.password ?? '');
    if (!isPasswordMatch) {
      throw new Error('Password incorrect.');
    }

    // MEMO: 不更新 updated_at (最後更新時間)
    await this.usersService.updateUserStatus({
      id: user.id,
      status: UserStatusEnum.VERIFIED,
      updatedAt: () => '"updated_at"',
    });

    return this.jwtService.sign({
      userId: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
    });
  }
}
