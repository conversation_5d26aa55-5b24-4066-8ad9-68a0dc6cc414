import { Injectable } from '@nestjs/common';
import { SignInByOktaResult } from '../entities/sign-in-by-okta.entity';
import {
  AuthFilter,
  AuthResponseFilterOriginalResult,
  AuthResponseFilterResult,
  AuthResponseFilterTypeEnum,
  SignInByOktaResponseFilterPayload,
  SignInByPasswordResponseFilterPayload,
} from './auth.filter.interface';
import { SignInByPasswordResult } from '../entities/sign-in-by-password.entity';

@Injectable()
export class AuthResponseFilter
  implements
    AuthFilter<AuthResponseFilterOriginalResult, AuthResponseFilterResult>
{
  getResponse(
    orig: AuthResponseFilterOriginalResult,
  ): AuthResponseFilterResult {
    switch (orig.type) {
      case AuthResponseFilterTypeEnum.SIGN_IN_BY_OKTA: {
        const payload = orig.payload as SignInByOktaResponseFilterPayload;
        const result = new SignInByOktaResult();
        result.token = payload.token;
        return result;
      }
      case AuthResponseFilterTypeEnum.SIGN_IN_BY_PASSWORD: {
        const payload = orig.payload as SignInByPasswordResponseFilterPayload;
        const result = new SignInByPasswordResult();
        result.token = payload.token;
        return result;
      }
      default:
    }
  }
}
