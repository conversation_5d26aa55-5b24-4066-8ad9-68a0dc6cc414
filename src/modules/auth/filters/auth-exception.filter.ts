import { HttpException, HttpStatus } from '@nestjs/common';
import { AuthFilter } from './auth.filter.interface';

export class AuthExceptionFilter implements AuthFilter<Error, HttpException> {
  getResponse(error: Error): HttpException {
    switch (error.message) {
      case 'This feature is not available.':
        return new HttpException('此功能不開放使用', HttpStatus.BAD_REQUEST);

      case 'Invalid Okta ID Token.':
        return new HttpException(
          '無效的 Okta ID Token',
          HttpStatus.UNAUTHORIZED,
        );

      case 'No account match that account.':
        return new HttpException(
          '帳號不存在，請洽管理人員',
          HttpStatus.NOT_FOUND,
        );

      case 'User status is suspended, please contact the manager.':
        return new HttpException(
          '帳號已被停用，請洽管理人員',
          HttpStatus.BAD_REQUEST,
        );

      case 'User status is unverified, please set password.':
        return new HttpException(
          '帳號尚未驗證，請先設定密碼',
          HttpStatus.BAD_REQUEST,
        );

      case 'Password login is not allowed.':
        return new HttpException('不允許使用密碼登入', HttpStatus.BAD_REQUEST);

      case 'Password incorrect.':
        return new HttpException('密碼錯誤', HttpStatus.UNAUTHORIZED);

      default:
        return new HttpException(
          error.message,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
    }
  }
}
