import { SignInByOktaResult } from '../entities/sign-in-by-okta.entity';
import { SignInByPasswordResult } from '../entities/sign-in-by-password.entity';

export interface AuthFilter<T, U> {
  getResponse(orig: T): U;
}

export enum AuthResponseFilterTypeEnum {
  SIGN_IN_BY_OKTA = 'SIGN_IN_BY_OKTA',
  SIGN_IN_BY_PASSWORD = 'SIGN_IN_BY_PASSWORD',
}

export interface SignInByOktaResponseFilterPayload {
  token: string;
}

export interface SignInByPasswordResponseFilterPayload {
  token: string;
}

export interface AuthResponseFilterOriginalResult {
  type: AuthResponseFilterTypeEnum;
  payload:
    | SignInByOktaResponseFilterPayload
    | SignInByPasswordResponseFilterPayload;
}

export type AuthResponseFilterResult =
  | SignInByOktaResult
  | SignInByPasswordResult;
