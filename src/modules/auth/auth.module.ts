import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { JwtModule } from '@nestjs/jwt';
import { UsersModule } from '../users/users.module';
import { AuthExceptionFilter } from './filters/auth-exception.filter';
import { AuthResponseFilter } from './filters/auth-response.filter';
import { PassportModule } from '@nestjs/passport';
import { UserStrategy } from './strategies/user.strategy';
import { UserSetPasswordStrategy } from './strategies/user-set-password.strategy';
import { UserResetPasswordStrategy } from './strategies/user-reset-password.strategy';

import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../../database/user.entity';
import { UnleashFeatureFlagsModule } from '../unleash-feature-flags/unleash-feature-flags.module';

@Module({
  imports: [
    PassportModule,
    TypeOrmModule.forFeature([User]),
    JwtModule,
    UsersModule,
    UnleashFeatureFlagsModule,
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    UserStrategy,
    UserSetPasswordStrategy,
    UserResetPasswordStrategy,
    AuthExceptionFilter,
    AuthResponseFilter,
  ],
})
export class AuthModule {}
