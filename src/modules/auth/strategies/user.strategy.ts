import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserStatusEnum } from '../../../database/user.entity';

@Injectable()
export class UserStrategy extends PassportStrategy(Strategy, 'user') {
  constructor(
    configService: ConfigService,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get('auth.user.secret'),
    });
  }

  async validate(payload: any) {
    const { status } = await this.userRepository.findOne({
      where: { id: payload.userId },
      select: ['status'],
    });

    if (status !== UserStatusEnum.VERIFIED) {
      throw new HttpException(
        '帳號已被停用，請洽管理人員',
        HttpStatus.UNAUTHORIZED,
      );
    }
    return payload;
  }
}
