import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserStatusEnum } from '../../../database/user.entity';

@Injectable()
export class UserResetPasswordStrategy extends PassportStrategy(
  Strategy,
  'user-reset-password',
) {
  constructor(
    configService: ConfigService,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get('sendMail.resetPassword.tokenSecret'),
      passReqToCallback: true,
    });
  }

  // MEMO: 若使用者狀態不是已驗證、重設密碼的 token 不符合不可以進行重設密碼
  async validate(req: any, payload: any) {
    const user = await this.userRepository.findOne({
      where: { id: payload.userId },
    });
    if (!user || user.deleterUserId) {
      throw new HttpException('帳號不存在，請洽管理人員', HttpStatus.NOT_FOUND);
    }
    const token = req.headers.authorization.substring(7);
    if (
      user.status != UserStatusEnum.VERIFIED ||
      token !== user.resetPasswordToken
    ) {
      throw new HttpException('連結已失效', HttpStatus.BAD_REQUEST);
    }

    return payload;
  }
}
