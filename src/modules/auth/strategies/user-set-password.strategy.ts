import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserStatusEnum } from '../../../database/user.entity';

@Injectable()
export class UserSetPasswordStrategy extends PassportStrategy(
  Strategy,
  'user-set-password',
) {
  constructor(
    configService: ConfigService,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get('sendMail.setPassword.tokenSecret'),
      passReqToCallback: true,
    });
  }

  async validate(req: any, payload: any) {
    const user = await this.userRepository.findOne({
      where: { id: payload.userId },
    });
    if (!user || user.deleterUserId) {
      throw new HttpException('帳號不存在，請洽管理人員', HttpStatus.NOT_FOUND);
    }
    const token = req.headers.authorization.substring(7);
    if (
      user.status !== UserStatusEnum.UNVERIFIED ||
      token !== user.setPasswordToken
    ) {
      throw new HttpException('連結已失效', HttpStatus.BAD_REQUEST);
    }
    return payload;
  }
}
