import { Controller, Post, Body, Headers, HttpCode } from '@nestjs/common';
import { AuthService } from './auth.service';
import { SignInByOktaDto } from './dto/sign-in-by-okta.dto';
import {
  ApiBearerAuth,
  ApiCreatedResponse,
  ApiOperation,
  ApiUnauthorizedResponse,
} from '@nestjs/swagger';
import {
  SignInByOktaResult,
  SignInByOktaRo,
} from './entities/sign-in-by-okta.entity';
import { AuthResponseFilter } from './filters/auth-response.filter';
import { AuthExceptionFilter } from './filters/auth-exception.filter';
import { AuthResponseFilterTypeEnum } from './filters/auth.filter.interface';
import { ApiResponseStatusEnum } from '../../interceptors/response/response.interface';
import {
  SignInByPasswordResult,
  SignInByPasswordRo,
} from './entities/sign-in-by-password.entity';
import { SignInByPasswordDto } from './dto/sign-in-by-password.dto';

@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly authExceptionFilter: AuthExceptionFilter,
    private readonly authResponseFilter: AuthResponseFilter,
  ) {}

  @Post('/login/okta')
  @HttpCode(201)
  @ApiBearerAuth('oktaToken')
  @ApiOperation({
    summary: '透過 okta 進行登入',
  })
  @ApiCreatedResponse({
    type: SignInByOktaRo,
  })
  @ApiUnauthorizedResponse({
    content: {
      'application/json': {
        examples: {
          'Okta 登入異常': {
            value: {
              code: 401,
              status: ApiResponseStatusEnum.FAILED,
              message: '無效的 Okta ID Token',
            },
          },
          帳號不存在: {
            value: {
              code: 401,
              status: ApiResponseStatusEnum.FAILED,
              message: '帳號不存在，請洽管理人員',
            },
          },
          帳號已停用: {
            value: {
              code: 401,
              status: ApiResponseStatusEnum.FAILED,
              message: '帳號已被停用，請洽管理人員',
            },
          },
        },
      },
    },
  })
  async signInByOkta(
    @Headers('authorization') authorization: string,
    @Body() signInByOktaDto: SignInByOktaDto,
  ): Promise<SignInByOktaResult> {
    try {
      const token = await this.authService.signInByOkta({
        authorization: authorization.split(' ')[1],
        nonce: signInByOktaDto.nonce,
      });
      return this.authResponseFilter.getResponse({
        type: AuthResponseFilterTypeEnum.SIGN_IN_BY_OKTA,
        payload: {
          token,
        },
      });
    } catch (error) {
      throw this.authExceptionFilter.getResponse(error);
    }
  }

  @Post('/login/password')
  @HttpCode(201)
  @ApiCreatedResponse({
    type: SignInByPasswordRo,
  })
  @ApiOperation({
    summary: '透過信箱密碼進行登入',
  })
  async signInByPassword(
    @Body() signInByPasswordDto: SignInByPasswordDto,
  ): Promise<SignInByPasswordResult> {
    try {
      const token = await this.authService.signInByPassword({
        email: signInByPasswordDto.email,
        password: signInByPasswordDto.password,
      });
      return this.authResponseFilter.getResponse({
        type: AuthResponseFilterTypeEnum.SIGN_IN_BY_PASSWORD,
        payload: {
          token,
        },
      });
    } catch (error) {
      throw this.authExceptionFilter.getResponse(error);
    }
  }
}
