import { ApiProperty } from '@nestjs/swagger';
import {
  ApiResponseStatusEnum,
  ResponseInterface,
} from '../../../interceptors/response/response.interface';
import { customApiPropertyDescription } from '../../../utils/swagger';

export class SignInByOktaResult {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'user token（JWT）',
    }),
    example: 'token',
  })
  token: string;
}

export class SignInByOktaRo implements ResponseInterface<SignInByOktaResult> {
  @ApiProperty({ example: 201 })
  code: number;

  @ApiProperty({
    enum: ApiResponseStatusEnum,
    example: ApiResponseStatusEnum.SUCCESS,
  })
  status: ApiResponseStatusEnum;

  @ApiProperty({ type: SignInByOktaResult })
  data: SignInByOktaResult;
}
