import { ApiProperty } from '@nestjs/swagger';
import {
  ApiResponseStatusEnum,
  ResponseInterface,
} from '../../../interceptors/response/response.interface';
import { customApiPropertyDescription } from '../../../utils/swagger';

export class SignInByPasswordResult {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'user token（JWT）',
    }),
    example: 'token',
  })
  token: string;
}

export class SignInByPasswordRo
  implements ResponseInterface<SignInByPasswordResult>
{
  @ApiProperty({ example: 201 })
  code: number;

  @ApiProperty({
    enum: ApiResponseStatusEnum,
    example: ApiResponseStatusEnum.SUCCESS,
  })
  status: ApiResponseStatusEnum;

  @ApiProperty({ type: SignInByPasswordResult })
  data: SignInByPasswordResult;
}
