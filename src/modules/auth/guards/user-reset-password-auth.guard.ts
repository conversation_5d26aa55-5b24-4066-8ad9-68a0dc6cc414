import {
  ExecutionContext,
  HttpException,
  HttpStatus,
  Injectable,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiResponseStatusEnum } from '../../../interceptors/response/response.interface';

@Injectable()
export default class UserResetPasswordAuthGuard extends AuthGuard(
  'user-reset-password',
) {
  canActivate(context: ExecutionContext) {
    return super.canActivate(context);
  }

  handleRequest(err, user) {
    if (err || !user) {
      throw new HttpException(
        {
          code: err.status || HttpStatus.UNAUTHORIZED,
          status: ApiResponseStatusEnum.FAILED,
          message: err.message || 'Unauthorized',
        },
        HttpStatus.UNAUTHORIZED,
      );
    }
    return user;
  }
}
