import { Inject, Injectable } from '@nestjs/common';
import { Enforcer } from 'casbin';
import { CheckPermissionTarget } from './casbin.interface';

@Injectable()
export class CasbinService {
  constructor(
    @Inject('CASBIN_ENFORCER')
    private enforcer: Enforcer,
  ) {}

  /**
   * @description 檢查權限
   */
  public async checkPermission(
    userId: number,
    target: CheckPermissionTarget,
  ): Promise<void> {
    const isAllowed = await this.enforcer.enforce(
      `user::${userId}`,
      target.permissionObjectKey,
      target.permissionActionKey,
    );
    if (!isAllowed) throw new Error('Authorization Forbidden');
  }

  /**
   * @description 透過角色 ID 取得使用者 IDs
   */
  async getUserIdsByRoleId(roleId: number): Promise<number[]> {
    return (await this.enforcer.getUsersForRole(`role::${roleId}`)).map(
      (idStr) => parseInt(idStr.replace('user::', ''), 10),
    );
  }
}
