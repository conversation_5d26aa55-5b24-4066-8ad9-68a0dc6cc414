import TypeORMAdapter from 'typeorm-adapter';
import { newEnforcer } from 'casbin';
import { DataSourceOptions } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { Module, Provider } from '@nestjs/common';
import { CasbinService } from './casbin.service';

const casbinEnforceProvider: Provider = {
  provide: 'CASBIN_ENFORCER',
  inject: [ConfigService],
  useFactory: async (configService: ConfigService) => {
    const adapter = await TypeORMAdapter.newAdapter({
      type: configService.get('database.type'),
      host: configService.get('database.host'),
      port: configService.get<number>('database.port'),
      username: configService.get('database.username'),
      password: configService.get('database.password'),
      database: configService.get('database.database'),
    } as DataSourceOptions);
    const enforcer = await newEnforcer(`${__dirname}/model.conf`, adapter);
    return enforcer;
  },
};

@Module({
  imports: [],
  providers: [casbinEnforceProvider, CasbinService],
  exports: [casbinEnforceProvider, CasbinService],
})
export class CasbinModule {}
