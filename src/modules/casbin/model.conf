[request_definition]
r = sub, obj, act 
# sub (主題/實體, 使用者/角色), obj (對象/資源, 東西(api)), act (操作/方式, 動作(CRUD))

[policy_definition]
p = sub, obj, act, eft
# sub (主題/實體, 使用者/角色), obj (對象/資源, 東西(api)), act (操作/方式, 動作(CRUD)), eft (允許(allow), 拒絕(deny))

[matchers]
m = g(r.sub, p.sub) && r.act == p.act && r.obj == p.obj
# m = g(r.sub, p.sub) && keyMatch2(r.obj, p.obj) && r.act == p.act
# 驗證器, keyMatch2 主要用來作 router source 配對

[policy_effect]
e = some(where (p.eft == allow))
# 只要有一個 policy 的 eft 是 allow 就可以通過

[role_definition]
g = _, _
# 角色定義，前項角色會繼承後項角色的權限