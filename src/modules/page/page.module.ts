import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Page } from 'src/database/page.entity';

import { Translation } from 'src/database/translation.entity';
import { PageAdminService } from 'src/modules/page/services/page-admin.service';
import { PageService } from 'src/modules/page/services/page.service';
import { TranslationModule } from 'src/modules/translation/translation.module';

@Module({
  imports: [TypeOrmModule.forFeature([Page, Translation]), TranslationModule],
  controllers: [],
  providers: [PageAdminService, PageService],
  exports: [PageAdminService, PageService],
})
export class PageModule {}
