import { PageTranslationFieldsWithLanguageCode } from 'src/modules/page/dto/create-page.dto';
import { PageRes } from 'src/modules/page/interfaces/page.interface';
import { OperatorRes } from 'src/shared/interfaces/operator.interface';

export class PageAdminRes extends PageRes {
  publishedAt?: number;
  createdAt: number;
  updatedAt: number;
  creator: OperatorRes;
  updater: OperatorRes;
  publisher?: OperatorRes;
}

export class PageDetailAdminRes extends PageAdminRes {
  pageContent: PageTranslationFieldsWithLanguageCode;
}
