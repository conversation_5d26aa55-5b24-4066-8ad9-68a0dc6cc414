import { ApiProperty } from '@nestjs/swagger';
import { IsInt, Min } from 'class-validator';
import { TransformToNumber } from 'src/decorators/transformers/TransformToNumber';
import { customApiPropertyDescription } from 'src/utils/swagger';

export class DeletePageByIdDto {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'Page ID',
      remarks: ['Must be a positive integer'],
    }),
    example: 1,
  })
  @IsInt()
  @Min(1)
  @TransformToNumber()
  readonly id: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'Operation user ID',
      remarks: ['Must be a positive integer'],
    }),
    example: 1,
  })
  @IsInt()
  @Min(1)
  @TransformToNumber()
  readonly operationUserId: number;
}
