import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsInt,
  IsObject,
  IsOptional,
  IsString,
  IsUrl,
  Length,
  Matches,
  Min,
  ValidateNested,
} from 'class-validator';
import { PageStatusEnum } from 'src/database/page.entity';
import { LanguageCodeEnum } from 'src/database/translation.entity';
import { IsNotBlank } from 'src/decorators/validations/IsNotBlank';
import { PageTranslationFields } from 'src/modules/page/dto/create-page.dto';
import { customApiPropertyDescription } from 'src/utils/swagger';

export class PageTranslationFieldsWithLanguageCode {
  @ApiProperty({
    description: 'Translations',
    type: () => PageTranslationFields,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => PageTranslationFields)
  [LanguageCodeEnum.EN]: PageTranslationFields;

  @ApiProperty({
    description: 'Translations',
    type: () => PageTranslationFields,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => PageTranslationFields)
  [LanguageCodeEnum.ZHTW]: PageTranslationFields;
}

export class UpdatePageDto {
  @Expose()
  @ApiPropertyOptional({
    description: customApiPropertyDescription({
      basic: 'Page slug identifier',
      remarks: [
        'Must be unique across all pages',
        'URL-friendly format (lowercase, hyphens)',
      ],
    }),
    example: 'keyshot-classic-customer-case-studies',
  })
  @IsString()
  @IsNotBlank()
  @Length(3, 255)
  @Matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, {
    message: 'Slug must be lowercase with hyphens only',
  })
  slug: string;

  @Expose()
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'Page status',
    }),
    enum: PageStatusEnum,
    example: PageStatusEnum.PUBLISHED,
  })
  @IsEnum(PageStatusEnum)
  status: PageStatusEnum;

  @ApiPropertyOptional({
    description: customApiPropertyDescription({
      basic: 'Parent page ID for hierarchical pages',
    }),
    example: 1,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  parentPageId?: number;

  @ApiPropertyOptional({
    description: 'List of topic ids to associate with this page',
    example: [1, 2],
  })
  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  topics: number[];

  @ApiPropertyOptional({
    description: customApiPropertyDescription({
      basic: 'Meta OG image URL for social media sharing',
      remarks: ['Should be absolute URL'],
    }),
    example: 'https://example.com/images/keyshot-case-study-og.jpg',
  })
  @IsOptional()
  @IsUrl()
  metaOgImage: string;

  @ApiPropertyOptional({
    description: customApiPropertyDescription({
      basic: 'SEO meta keywords',
    }),
    example: 'keyshot, 3d rendering, product design, case study, visualization',
  })
  @IsOptional()
  @IsString()
  @Length(10, 500)
  metaKeywords: string;

  @ApiPropertyOptional({
    description: customApiPropertyDescription({
      basic: 'Logo URL',
    }),
    example: 'https://example.com/images/keyshot-logo.png',
  })
  @IsOptional()
  @IsString()
  @IsNotBlank()
  logoUrl: string;

  @ApiPropertyOptional({
    description: 'Hashtags Ids',
    example: [1, 2],
  })
  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  hashtags: number[];

  @ApiProperty({
    description: 'Translations',
    type: () => PageTranslationFieldsWithLanguageCode,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => PageTranslationFieldsWithLanguageCode)
  pageContent: PageTranslationFieldsWithLanguageCode;
}
