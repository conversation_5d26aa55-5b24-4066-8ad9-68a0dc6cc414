import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsInt,
  IsObject,
  IsOptional,
  IsString,
  IsUrl,
  Length,
  Matches,
  Min,
  ValidateNested,
} from 'class-validator';
import { PageStatusEnum, PageTypeEnum } from 'src/database/page.entity';
import { IsNotBlank } from 'src/decorators/validations/IsNotBlank';
import { customApiPropertyDescription } from 'src/utils/swagger';
// import { ValidateContentByType } from 'src/decorators/validations/ValidateContentByType';
import { LanguageCodeEnum } from 'src/database/translation.entity';

export class MetaGeneralFields {
  @ApiPropertyOptional({
    description: customApiPropertyDescription({
      basic: 'Meta OG title for social media sharing',
      remarks: ['Optimal length: 50 characters'],
    }),
    example: 'KeyShot Case Studies - Design Excellence',
  })
  @IsOptional()
  @IsString()
  @Length(0, 50)
  metaOgTitle?: string;

  @ApiPropertyOptional({
    description: customApiPropertyDescription({
      basic: 'Meta OG description for social media sharing',
      remarks: ['Optimal length: 120 characters'],
    }),
    example:
      'Discover how leading design teams use KeyShot to create stunning visualizations and accelerate product development.',
  })
  @IsOptional()
  @IsString()
  @Length(0, 120)
  metaOgDescription?: string;
}

export class PerformanceStatisticItemDto {
  @ApiProperty({ example: 'Rendering Speed' })
  @IsString()
  text: string;

  @ApiProperty({ example: '2x faster' })
  @IsString()
  value: string;
}

export class PageTranslationFields extends MetaGeneralFields {
  @ApiPropertyOptional({
    description: customApiPropertyDescription({
      basic: 'HTML content',
      remarks: ['Used for case title'],
    }),
    example: '<h1>KeyShot Classic Customer Case Studies</h1>',
  })
  @IsOptional()
  @IsString()
  @IsNotBlank()
  title?: string;
}

export class PageTranslationFieldsWithLanguageCode {
  @ApiProperty({
    description: 'Translations',
    type: () => PageTranslationFields,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => PageTranslationFields)
  [LanguageCodeEnum.EN]: PageTranslationFields;

  @ApiProperty({
    description: 'Translations',
    type: () => PageTranslationFields,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => PageTranslationFields)
  [LanguageCodeEnum.ZHTW]: PageTranslationFields;
}

export class CreatePageDto {
  @Expose()
  @ApiPropertyOptional({
    description: customApiPropertyDescription({
      basic: 'Page slug identifier',
      remarks: [
        'Must be unique across all pages',
        'URL-friendly format (lowercase, hyphens)',
      ],
    }),
    example: 'keyshot-classic-customer-case-studies',
  })
  @IsString()
  @IsNotBlank()
  @Length(3, 255)
  @Matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, {
    message: 'Slug must be lowercase with hyphens only',
  })
  slug: string;

  @Expose()
  @ApiPropertyOptional({
    description: customApiPropertyDescription({
      basic: 'Page type',
    }),
    enum: PageTypeEnum,
    example: PageTypeEnum.CASE_STUDY_LIST,
  })
  @IsEnum(PageTypeEnum)
  pageType: PageTypeEnum;

  @Expose()
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'Page status',
    }),
    enum: PageStatusEnum,
    example: PageStatusEnum.PUBLISHED,
  })
  @IsEnum(PageStatusEnum)
  status: PageStatusEnum;

  @ApiPropertyOptional({
    description: customApiPropertyDescription({
      basic: 'Parent page ID for hierarchical pages',
    }),
    example: 1,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  parentPageId?: number;

  @ApiPropertyOptional({
    description: 'List of topic ids to associate with this page',
    example: [1, 2],
  })
  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  topics: number[];

  @ApiPropertyOptional({
    description: customApiPropertyDescription({
      basic: 'Meta OG image URL for social media sharing',
      remarks: ['Should be absolute URL'],
    }),
    example: 'https://example.com/images/keyshot-case-study-og.jpg',
  })
  @IsOptional()
  @IsUrl()
  metaOgImage: string;

  @ApiPropertyOptional({
    description: customApiPropertyDescription({
      basic: 'SEO meta keywords',
    }),
    example: 'keyshot, 3d rendering, product design, case study, visualization',
  })
  @IsOptional()
  @IsString()
  @Length(10, 500)
  metaKeywords: string;

  @ApiPropertyOptional({
    description: customApiPropertyDescription({
      basic: 'Logo URL',
    }),
    example: 'https://example.com/images/keyshot-logo.png',
  })
  @IsOptional()
  @IsString()
  @IsNotBlank()
  logoUrl: string;

  @ApiPropertyOptional({
    description: 'Hashtags Ids',
    example: [1, 2],
  })
  @IsOptional()
  @IsArray()
  @IsInt({ each: true })
  hashtags: number[];

  @ApiProperty({
    description: 'Translations',
    type: () => PageTranslationFieldsWithLanguageCode,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => PageTranslationFieldsWithLanguageCode)
  pageContent: PageTranslationFieldsWithLanguageCode;
}
