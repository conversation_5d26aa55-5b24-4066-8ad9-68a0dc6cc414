import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { Page, PAGE_TABLE_NAME, PageTypeEnum } from 'src/database/page.entity';

import { TranslationService } from 'src/modules//translation/services/translation.services';

import { ConfigService } from '@nestjs/config';
import { ApiMetadataHeaders } from 'src/decorators/api-metadata/api-metadata.interface';
import { GetListPageOptionDto } from 'src/modules/page/dto/get-list-page-option.dto';

import {
  PageDetailRes,
  PageRes,
} from 'src/modules/page/interfaces/page.interface';
import { PaginationRes } from 'src/shared/interfaces/pagination.interface';
import { Repository } from 'typeorm';

@Injectable()
export class PageService {
  private readonly frontendUrl: string;

  constructor(
    @InjectRepository(Page)
    private readonly pageRepository: Repository<Page>,
    private readonly translationService: TranslationService,
    private readonly configService: ConfigService,
  ) {
    this.frontendUrl = this.configService.get('shared.restful.frontend.url');
  }

  async findPagesByType(
    pageType: PageTypeEnum,
    opts: GetListPageOptionDto,
  ): Promise<PaginationRes<Page>> {
    const query = this.pageRepository
      .createQueryBuilder('page')
      .andWhere('page.pageType = :pageType and page.deletedAt is NULL', {
        pageType: pageType,
      });

    if (opts.topicId) {
      query.innerJoin('page.topicPages', 'topicPage');
      query.innerJoin('topicPage.topic', 'topic');
      query.andWhere('topic.id = :topicId', { topicId: opts.topicId });
    }

    query
      .orderBy('page.publishedAt', 'DESC')
      .addOrderBy('page.createdAt', 'DESC')
      .addOrderBy('page.updatedAt', 'DESC');

    if (opts.page && opts.size) {
      query.skip((opts.page - 1) * opts.size).take(opts.size);
    }

    const [pages, total] = await query.getManyAndCount();

    return PaginationRes.create(pages, total, opts.page, opts.size);
  }

  async findPageByType(pageType: PageTypeEnum): Promise<Page> {
    const page = await this.pageRepository.findOne({
      where: { pageType, deletedAt: null },
    });

    if (!page) {
      throw new NotFoundException(`Page with type ${pageType} not found`);
    }

    return page;
  }

  async findPageByTypeAndId(pageType: PageTypeEnum, id: number): Promise<Page> {
    const page = await this.pageRepository.findOne({
      where: { pageType, id, deletedAt: null },
    });

    if (!page) {
      throw new NotFoundException(`Page with type ${pageType} not found`);
    }

    return page;
  }

  async getPageDetail(
    page: Page,
    metadata: ApiMetadataHeaders,
  ): Promise<PageDetailRes> {
    const langCode = metadata.language;
    const translations = await this.translationService.getTranslationsByTable(
      page.id,
      PAGE_TABLE_NAME,
      langCode,
    );

    const pageResponse = this.mapPageToDetailResponseDto(page);

    const response = {
      ...pageResponse,
      ...translations,
    };
    return response;
  }

  async getPages(
    pages: Page[],
    metadata: ApiMetadataHeaders,
  ): Promise<PageRes[]> {
    const pageIds = pages.map((p) => p.id);

    const translations = await this.translationService.getTranslationsByTable(
      pageIds,
      PAGE_TABLE_NAME,
      metadata.language,
    );

    const response = pages.map((page) => {
      const translation = translations[page.id];
      return {
        ...this.mapPageToDetailResponseDto(page),
        ...translation,
      };
    });

    return response;
  }

  private mapPageToDetailResponseDto(page: Page): PageDetailRes {
    return {
      id: page.id,
      slug: page.slug,
      title: page.title,
      metaOgTitle: page.metaOgTitle,
      metaOgDescription: page.metaOgDescription,
      metaOgImage: page.metaOgImage,
      metaKeywords: page.metaKeywords,
      pageType: page.pageType,
      status: page.status,
      parentPageId: page.parentPageId,
    };
  }
}
