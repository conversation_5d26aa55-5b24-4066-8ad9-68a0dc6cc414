import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  Page,
  PAGE_TABLE_NAME,
  PageStatusEnum,
  PageTypeEnum,
} from 'src/database/page.entity';
import { LanguageCodeEnum } from 'src/database/translation.entity';
import {
  CreatePageDto,
  PageTranslationFieldsWithLanguageCode,
} from 'src/modules/page/dto/create-page.dto';
import { GetListPageOptionDto } from 'src/modules/page/dto/get-list-page-option.dto';
import { TogglePublishPageDto } from 'src/modules/page/dto/toggle-publish-page.dto';
import {
  PageAdminRes,
  PageDetailAdminRes,
} from 'src/modules/page/interfaces/page-admin.interface';
import { TranslationService } from 'src/modules/translation/services/translation.services';
import { mapUserToOperatorResponseDto } from 'src/shared/interfaces/operator.interface';
import { PaginationRes } from 'src/shared/interfaces/pagination.interface';
import { In, Repository } from 'typeorm';
@Injectable()
export class PageAdminService {
  constructor(
    @InjectRepository(Page)
    private readonly pageRepository: Repository<Page>,
    private readonly translationService: TranslationService,
  ) {}
  async findPagesByType(
    pageType: PageTypeEnum,
    opts: GetListPageOptionDto,
  ): Promise<PaginationRes<Page>> {
    const query = this.pageRepository
      .createQueryBuilder('page')
      .andWhere('page.pageType = :pageType and page.deletedAt is NULL', {
        pageType: pageType,
      })
      .leftJoinAndSelect('page.creator', 'creator')
      .leftJoinAndSelect('page.updater', 'updater')
      .leftJoinAndSelect('page.publisher', 'publisher');
    if (opts.topicId) {
      query.andWhere('topic.id = :topicId', { topicId: opts.topicId });
    }
    query
      .orderBy('page.publishedAt', 'DESC')
      .addOrderBy('page.createdAt', 'DESC')
      .addOrderBy('page.updatedAt', 'DESC');
    if (opts.page && opts.size) {
      query.skip((opts.page - 1) * opts.size).take(opts.size);
    }
    const [pages, total] = await query.getManyAndCount();
    return PaginationRes.create(pages, total, opts.page, opts.size);
  }
  async findPageByType(pageType: PageTypeEnum): Promise<Page> {
    const page = await this.pageRepository.findOne({
      where: { pageType, deletedAt: null },
      relations: ['creator', 'updater', 'publisher'],
    });
    if (!page) {
      throw new NotFoundException(`Page with type ${pageType} not found`);
    }
    return page;
  }
  async findPageByTypeAndId(pageType: PageTypeEnum, id: number): Promise<Page> {
    const page = await this.pageRepository.findOne({
      where: { pageType, id, deletedAt: null },
      relations: ['creator', 'updater', 'publisher'],
    });
    if (!page) {
      throw new NotFoundException(`Page with type ${pageType} not found`);
    }
    return page;
  }
  async findPageBySlug(slug: string): Promise<Page> {
    const page = await this.pageRepository.findOne({
      where: { slug, deletedAt: null },
      relations: ['creator', 'updater', 'publisher'],
    });
    if (!page) {
      throw new NotFoundException(`Page with slug ${slug} not found`);
    }
    return page;
  }
  getPage(page: Page): PageAdminRes {
    return {
      id: page.id,
      slug: page.slug,
      title: page.title,
      metaOgTitle: page.metaOgTitle,
      metaOgDescription: page.metaOgDescription,
      metaOgImage: page.metaOgImage,
      metaKeywords: page.metaKeywords,
      pageType: page.pageType,
      status: page.status,
      parentPageId: page.parentPageId,
      publishedAt: page.publishedAt ? Number(page.publishedAt) : undefined,
      createdAt: page.createdAt ? Number(page.createdAt) : 0,
      updatedAt: page.updatedAt ? Number(page.updatedAt) : 0,
      creator: mapUserToOperatorResponseDto(page.creator),
      updater: mapUserToOperatorResponseDto(page.updater),
      publisher: page.publisher
        ? mapUserToOperatorResponseDto(page.publisher)
        : undefined,
    };
  }
  async getPageDetail(page: Page): Promise<PageDetailAdminRes> {
    const pageTranslations =
      await this.translationService.getTranslationsByTable(
        page.id,
        PAGE_TABLE_NAME,
        LanguageCodeEnum.EN,
      );
    const pageTranslationsZh =
      await this.translationService.getTranslationsByTable(
        page.id,
        PAGE_TABLE_NAME,
        LanguageCodeEnum.ZHTW,
      );
    const pageContent: PageTranslationFieldsWithLanguageCode = {
      [LanguageCodeEnum.EN]: {
        metaOgTitle: pageTranslations.metaOgTitle || undefined,
        metaOgDescription: pageTranslations.metaOgDescription || undefined,
        title: pageTranslations.title || undefined,
      },
      [LanguageCodeEnum.ZHTW]: {
        metaOgTitle: pageTranslationsZh.metaOgTitle || undefined,
        metaOgDescription: pageTranslationsZh.metaOgDescription || undefined,
        title: pageTranslationsZh.title || undefined,
      },
    };
    return {
      id: page.id,
      pageType: page.pageType,
      title: page.title,
      slug: page.slug,
      status: page.status,
      parentPageId: page.parentPageId,
      metaOgImage: page.metaOgImage,
      metaKeywords: page.metaKeywords,
      logoUrl: page.logoUrl,
      pageContent,
      creator: mapUserToOperatorResponseDto(page.creator),
      updater: mapUserToOperatorResponseDto(page.updater),
      publisher: page.publisher
        ? mapUserToOperatorResponseDto(page.publisher)
        : undefined,
      createdAt: Number(page.createdAt),
      updatedAt: Number(page.updatedAt),
      publishedAt: page.publishedAt ? Number(page.publishedAt) : undefined,
    };
  }
  async createPage(dto: CreatePageDto, operationUserId: number): Promise<Page> {
    const pageType = dto.pageType || PageTypeEnum.CASE_STUDY_LIST;
    const { pageContent } = dto;
    const existingPage = await this.pageRepository.findOne({
      where: { slug: dto.slug, pageType, deletedAt: null },
    });
    if (existingPage) {
      throw new Error('Page with this slug already exists');
    }
    const page = this.pageRepository.create({
      pageType,
      status: dto.status,
      creatorUserId: operationUserId,
      updaterUserId: operationUserId,
      slug: dto.slug,
      logoUrl: dto.logoUrl,
      parentPageId: dto.parentPageId,
      title: pageContent[LanguageCodeEnum.EN].title,
      metaOgTitle: pageContent[LanguageCodeEnum.EN].metaOgTitle,
      metaOgDescription: pageContent[LanguageCodeEnum.EN].metaOgDescription,
      metaOgImage: dto.metaOgImage,
      metaKeywords: dto.metaKeywords,
    });
    if (dto.status == PageStatusEnum.PUBLISHED) {
      page.publishedAt = BigInt(Date.now());
      page.publisherUserId = operationUserId;
    }
    await this.pageRepository.save(page);
    return page;
  }
  async togglePublishPage(dto: TogglePublishPageDto): Promise<void> {
    const pageType = PageTypeEnum.CASE_STUDY_LIST;
    const id = dto.id;
    const operationUserId = dto.operationUserId;
    const page = await this.pageRepository.findOne({
      where: { id: id, pageType, deletedAt: null },
    });
    if (!page)
      throw new NotFoundException(`Page with type ${pageType} not found`);
    if (page.status === PageStatusEnum.PUBLISHED) {
      page.status = PageStatusEnum.UNPUBLISHED;
      page.publishedAt = null;
      page.publisherUserId = null;
    } else {
      page.status = PageStatusEnum.PUBLISHED;
      page.publishedAt = BigInt(Date.now());
      page.publisherUserId = operationUserId;
    }
    await this.pageRepository.update(page.id, {
      status: page.status,
      publishedAt: page.publishedAt,
      publisherUserId: page.publisherUserId,
    });
  }
  async deletePages(pageIds: number[], operationUserId: number): Promise<void> {
    if (!pageIds || pageIds.length === 0) {
      throw new NotFoundException('No page IDs provided');
    }
    const pages = await this.pageRepository.find({
      where: { id: In(pageIds), deletedAt: null },
    });
    if (pages.length === 0) {
      throw new NotFoundException(
        `Pages with IDs ${pageIds.join(', ')} not found`,
      );
    }
    await this.pageRepository.update(pageIds, {
      deletedAt: BigInt(Date.now()),
      deleterUserId: operationUserId,
    });
  }
  private parseTranslationValue(value: any): string {
    if (!value) return value;
    if (
      typeof value === 'string' &&
      !value.startsWith('{') &&
      !value.startsWith('[')
    ) {
      return value;
    }
    try {
      return typeof value === 'string' ? JSON.parse(value) : value;
    } catch (e) {
      return value;
    }
  }
  private convertBigIntToNumber(obj: any): any {
    if (Array.isArray(obj)) {
      return obj.map((item) => this.convertBigIntToNumber(item));
    } else if (obj && typeof obj === 'object') {
      const newObj: any = {};
      for (const key in obj) {
        if (typeof obj[key] === 'bigint') {
          newObj[key] = Number(obj[key]);
        } else if (typeof obj[key] === 'object' && obj[key] !== null) {
          newObj[key] = this.convertBigIntToNumber(obj[key]);
        } else {
          newObj[key] = obj[key];
        }
      }
      return newObj;
    }
    return obj;
  }
}
