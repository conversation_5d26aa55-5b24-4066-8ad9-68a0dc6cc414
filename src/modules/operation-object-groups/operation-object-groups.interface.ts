import { OperationLogTargetTypeEnum } from '../../database/operation_log.entity';

/* eslint-disable @typescript-eslint/no-namespace */
export namespace OperationConfig {
  export namespace AUTHORIZATION_MANAGEMENT {
    export const operationObjectGroupKey = 'AUTHORIZATION_MANAGEMENT';
    export const operationObjectGroupName = '權限管理';
    export const operationObjectPosition = 1;

    export namespace USER_MANAGEMENT {
      export const operationObjectKey = `${operationObjectGroupKey}_USER_MANAGEMENT`;
      export const operationObjectName = '使用者設定';
      export const operationObjectPosition = 1;

      export const CREATE_USER = {
        operationObjectGroupKey,
        operationObjectKey,
        operationKey: `${operationObjectKey}_CREATE_USER`,
        operationName: '新增使用者',
        operationPosition: 1,
        targetType: OperationLogTargetTypeEnum.USER,
      };

      export const GET_USER_LIST = {
        operationObjectGroupKey,
        operationObjectKey,
        operationKey: `${operationObjectKey}_GET_USER_LIST`,
        operationName: '查詢使用者清單',
        operationPosition: 2,
        targetType: OperationLogTargetTypeEnum.USER,
      };

      export const GET_USER_DETAIL = {
        operationObjectGroupKey,
        operationObjectKey,
        operationKey: `${operationObjectKey}_GET_USER_DETAIL`,
        operationName: '查詢使用者',
        operationPosition: 3,
        targetType: OperationLogTargetTypeEnum.USER,
      };

      export namespace UPDATE_USER {
        const targetType = OperationLogTargetTypeEnum.USER;
        export const operationKey = `${operationObjectKey}_UPDATE_USER`;
        export const operationName = '編輯使用者';
        export const operationPosition = 4;

        export const UPDATE_INFO = {
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          operationEventKey: `${operationKey}_UPDATE_INFO`,
          operationEventName: '更改資訊',
          operationEventPosition: 1,
          targetType,
        };

        export const ENABLE = {
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          operationEventKey: `${operationKey}_ENABLE`,
          operationEventName: '啟用',
          operationEventPosition: 2,
          targetType,
        };

        export const DISABLE = {
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          operationEventKey: `${operationKey}_DISABLE`,
          operationEventName: '停用',
          operationEventPosition: 3,
          targetType,
        };
      }

      export const DELETE_USER = {
        operationObjectGroupKey,
        operationObjectKey,
        operationKey: `${operationObjectKey}_DELETE_USER`,
        operationName: '刪除使用者',
        operationPosition: 5,
        targetType: OperationLogTargetTypeEnum.USER,
      };
    }

    export namespace ROLE_MANAGEMENT {
      export const operationObjectKey = `${operationObjectGroupKey}_ROLE_MANAGEMENT`;
      export const operationObjectName = '角色設定';
      export const operationObjectPosition = 2;

      export const CREATE_ROLE = {
        operationObjectGroupKey,
        operationObjectKey,
        operationKey: `${operationObjectKey}_CREATE_ROLE`,
        operationName: '新增角色',
        operationPosition: 1,
        targetType: OperationLogTargetTypeEnum.ROLE,
      };

      export const GET_ROLE_LIST = {
        operationObjectGroupKey,
        operationObjectKey,
        operationKey: `${operationObjectKey}_GET_ROLE_LIST`,
        operationName: '查詢角色清單',
        operationPosition: 2,
        targetType: OperationLogTargetTypeEnum.ROLE,
      };

      export const GET_ROLE_DETAIL = {
        operationObjectGroupKey,
        operationObjectKey,
        operationKey: `${operationObjectKey}_GET_ROLE_DETAIL`,
        operationName: '查詢角色',
        operationPosition: 3,
        targetType: OperationLogTargetTypeEnum.ROLE,
      };

      export const UPDATE_ROLE = {
        operationObjectGroupKey,
        operationObjectKey,
        operationKey: `${operationObjectKey}_UPDATE_ROLE`,
        operationName: '編輯角色',
        operationPosition: 4,
        targetType: OperationLogTargetTypeEnum.ROLE,
      };

      export const DELETE_ROLE = {
        operationObjectGroupKey,
        operationObjectKey,
        operationKey: `${operationObjectKey}_DELETE_ROLE`,
        operationName: '刪除角色',
        operationPosition: 5,
        targetType: OperationLogTargetTypeEnum.ROLE,
      };
    }
  }
  export namespace TERMS_MANAGEMENT {
    export const operationObjectGroupKey = 'TERMS_MANAGEMENT';
    export const operationObjectGroupName = '條款管理';
    export const operationObjectPosition = 2;

    export namespace TERMS_LIST {
      export const operationObjectKey = `${operationObjectGroupKey}_TERMS_LIST`;
      export const operationObjectName = '條款列表';
      export const operationObjectPosition = 1;

      export const GET_TERMS_LIST = {
        operationObjectGroupKey,
        operationObjectKey,
        operationKey: `${operationObjectKey}_GET_TERMS_LIST`,
        operationName: '查詢條款清單',
        operationPosition: 1,
        targetType: OperationLogTargetTypeEnum.TERMS,
      };

      export const CREATE_TERMS_VERSION = {
        operationObjectGroupKey,
        operationObjectKey,
        operationKey: `${operationObjectKey}_CREATE_TERMS_VERSION`,
        operationName: '新增條款版本',
        operationPosition: 2,
        targetType: OperationLogTargetTypeEnum.TERMS,
      };

      export const GET_TERMS_VERSION_LIST = {
        operationObjectGroupKey,
        operationObjectKey,
        operationKey: `${operationObjectKey}_GET_TERMS_VERSION_LIST`,
        operationName: '查詢條款版本清單',
        operationPosition: 3,
        targetType: OperationLogTargetTypeEnum.TERMS,
      };

      export const GET_TERMS_VERSION_DETAIL = {
        operationObjectGroupKey,
        operationObjectKey,
        operationKey: `${operationObjectKey}_GET_TERMS_VERSION_DETAIL`,
        operationName: '查詢條款版本細節',
        operationPosition: 4,
        targetType: OperationLogTargetTypeEnum.TERMS,
      };
    }
  }
}
