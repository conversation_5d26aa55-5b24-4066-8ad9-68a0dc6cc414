import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Context, startUnleash, Unleash } from 'unleash-client';

@Injectable()
export class UnleashFeatureFlagsService implements OnModuleInit {
  private unleash: Unleash | null = null;

  constructor(private configService: ConfigService) {}

  async onModuleInit(): Promise<void> {
    await this.init();
  }

  private async init(): Promise<void> {
    if (this.unleash) {
      throw new Error(
        'UnleashFeatureFlagsService has already been initialized.',
      );
    }

    const unleashConfig = {
      apiUrl: this.configService.get('unleash.apiUrl'),
      appName: this.configService.get('unleash.appName'),
      refreshInterval: this.configService.get('unleash.refreshInterval'),
      apiToken: this.configService.get('unleash.apiToken'),
    };

    if (Object.values(unleashConfig).some((value) => !value)) {
      console.warn(
        '[Unleash configuration is incomplete. Initialization skipped.]',
      );
      return;
    }

    try {
      this.unleash = await startUnleash({
        url: unleashConfig.apiUrl,
        appName: unleashConfig.appName,
        refreshInterval: unleashConfig.refreshInterval,
        customHeaders: {
          Authorization: unleashConfig.apiToken,
        },
      });
    } catch (error) {
      console.error('Unleash initialization failed.', error.message);
    }
  }

  async isEnabled(name: string, context?: Context): Promise<boolean> {
    await this.ensureInitialized();
    return this.unleash.isEnabled(name, context);
  }

  private async ensureInitialized(): Promise<void> {
    if (!this.unleash) {
      await this.init();
    }
  }
}
