import { UserStatusEnum } from '../../../database/user.entity';
import { CreateUserResult } from '../entities/create-user.entity';
import { GetUserListResult } from '../entities/get-user-list.entity';
import { GetUserDetailResult } from '../entities/get-user-detail.entity';
import { CreateUserOperationLogRes } from '../users.interface';

type CreatorOrUpdater = {
  firstName: string;
  lastName: string;
  email: string;
};

export enum UsersResponseFilterTypeEnum {
  CREATE_USER = 'CREATE_USER',
  GET_USER_LIST = 'GET_USER_LIST',
  GET_USER_DETAIL = 'GET_USER_DETAIL',
}

export interface UsersFilter<T, U> {
  getResponse(orig: T, operationLogResult?: CreateUserOperationLogRes): U;
}

export interface CreateUserResponseFilterPayload {
  id: number;
}

export interface GetUserListResponseFilterPayload {
  users: {
    id: number;
    firstName: string;
    lastName: string;
    department: string;
    title: string;
    email: string;
    status: UserStatusEnum;
    creator: CreatorOrUpdater;
    createdAt: number;
    updater: CreatorOrUpdater;
    updatedAt: number;
    roles: { id: number; name: string }[];
  }[];
  totalCount: number;
  page: number;
  size: number;
}

export interface GetUserDetailResponseFilterPayload {
  id: number;
  firstName: string;
  lastName: string;
  department: string;
  title: string;
  email: string;
  status: UserStatusEnum;
  roles: { id: number; name: string }[];
}

export interface UsersResponseFilterOriginalResult {
  type: UsersResponseFilterTypeEnum;
  payload:
    | CreateUserResponseFilterPayload
    | GetUserListResponseFilterPayload
    | GetUserDetailResponseFilterPayload;
}

export type UsersResponseFilterResult =
  | CreateUserResult
  | GetUserListResult
  | GetUserDetailResult;
