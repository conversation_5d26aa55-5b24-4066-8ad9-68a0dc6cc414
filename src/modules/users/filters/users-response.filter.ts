import { Injectable } from '@nestjs/common';
import {
  CreateUserResponseFilterPayload,
  UsersFilter,
  GetUserListResponseFilterPayload,
  UsersResponseFilterOriginalResult,
  GetUserDetailResponseFilterPayload,
  UsersResponseFilterResult,
  UsersResponseFilterTypeEnum,
} from './users.filter.interface';
import { CreateUserResult } from '../entities/create-user.entity';
import { GetUserListResult } from '../entities/get-user-list.entity';
import { GetUserDetailResult } from '../entities/get-user-detail.entity';

@Injectable()
export class UsersResponseFilter
  implements
    UsersFilter<UsersResponseFilterOriginalResult, UsersResponseFilterResult>
{
  getResponse(
    orig: UsersResponseFilterOriginalResult,
  ): UsersResponseFilterResult {
    switch (orig.type) {
      case UsersResponseFilterTypeEnum.GET_USER_LIST: {
        const payload = orig.payload as GetUserListResponseFilterPayload;
        let result = new GetUserListResult();
        result = {
          users: payload.users,
          totalCount: payload.totalCount,
          totalPage: Math.ceil(payload.totalCount / payload.size),
          currentPage: payload.page,
        };
        return result;
      }

      case UsersResponseFilterTypeEnum.CREATE_USER: {
        const payload = orig.payload as CreateUserResponseFilterPayload;
        const result = new CreateUserResult();
        result.id = payload.id;
        return result;
      }

      case UsersResponseFilterTypeEnum.GET_USER_DETAIL: {
        const payload = orig.payload as GetUserDetailResponseFilterPayload;
        let result = new GetUserDetailResult();
        result = {
          id: payload.id,
          department: payload.department,
          title: payload.title,
          lastName: payload.lastName,
          firstName: payload.firstName,
          email: payload.email,
          status: payload.status,
          roleIds: payload.roles.map((role) => role.id),
        };
        return result;
      }
    }
  }
}
