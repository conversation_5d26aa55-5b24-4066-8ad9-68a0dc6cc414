import { HttpException, HttpStatus } from '@nestjs/common';
import { UsersFilter } from './users.filter.interface';
import { adminRoleInfo } from '../../../commands/seeds/admin-role.seed';
import { CreateUserOperationLogRes } from '../users.interface';

export class UsersExceptionFilter implements UsersFilter<Error, HttpException> {
  getResponse(
    { message: originalErrorMessage }: Error,
    operationLogResult?: CreateUserOperationLogRes,
  ): HttpException {
    let errorMessage: string;
    let statusCode: number;

    switch (originalErrorMessage) {
      case 'Contains invalid roleId(s).':
        errorMessage = '綁定的角色不存在';
        statusCode = HttpStatus.BAD_REQUEST;
        break;

      case 'User not found.':
        errorMessage = '查無使用者';
        statusCode = HttpStatus.NOT_FOUND;
        break;

      case "Can't delete the user that status is verified.":
        errorMessage = '使用者的狀態非邀請中或停用，無法刪除';
        statusCode = HttpStatus.BAD_REQUEST;
        break;

      case "Can't update the user role because the system admin role of the user that is the last one user.":
        errorMessage = `使用者為「${adminRoleInfo.name}」角色下的唯一使用者。若要編輯，請先設定其他已啟用使用者為「${adminRoleInfo.name}」`;
        statusCode = HttpStatus.BAD_REQUEST;
        break;

      case "Can't disable the user role because the system admin role of the user that is the last one user.":
        errorMessage = `使用者為「${adminRoleInfo.name}」角色下的唯一使用者。若要停用，請先設定其他已啟用使用者為「${adminRoleInfo.name}」`;
        statusCode = HttpStatus.BAD_REQUEST;
        break;

      case "Can't delete the user role because the system admin role of the user that is the last one user.":
        errorMessage = `使用者為「${adminRoleInfo.name}」角色下的唯一使用者。若要刪除，請先設定其他已啟用使用者為「${adminRoleInfo.name}」`;
        statusCode = HttpStatus.BAD_REQUEST;
        break;

      case `duplicate key value violates unique constraint "unique_email_constraint"`:
        errorMessage = '信箱與其他使用者重複';
        statusCode = HttpStatus.BAD_REQUEST;
        break;

      default:
        errorMessage = originalErrorMessage;
        statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
    }

    return new HttpException(
      {
        message: errorMessage,
        operationLogResult,
      },
      statusCode,
    );
  }
}
