import { ApiProperty } from '@nestjs/swagger';

import { UserStatusEnum } from '../../../database/user.entity';
import {
  ResponseInterface,
  LogResponseStatusEnum,
  ApiResponseStatusEnum,
} from '../../../interceptors/response/response.interface';
import { customApiPropertyDescription } from '../../../utils/swagger';

export class GetUserDetailResult {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'ID',
    }),
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '部門',
    }),
    example: '產品研發部',
  })
  department: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '職稱',
    }),
    example: 'RD',
  })
  title: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '姓氏',
    }),
    example: '姓氏',
  })
  lastName: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '名字',
    }),
    example: '名字',
  })
  firstName: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '信箱',
    }),
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '所綁定的角色 IDs',
      remarks: ['對應前端「角色」欄位'],
    }),
    type: [Number],
    example: [1, 2],
  })
  roleIds: number[];

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '狀態',
      enumTypeDescription: [
        `${UserStatusEnum.UNVERIFIED}：停用`,
        `${UserStatusEnum.VERIFIED}：啟用`,
        `${UserStatusEnum.SUSPENDED}：邀請中`,
      ],
    }),
    enum: UserStatusEnum,
    example: UserStatusEnum.VERIFIED,
  })
  status: UserStatusEnum;
}

export class GetUserDetailRo implements ResponseInterface<GetUserDetailResult> {
  @ApiProperty({ example: 200 })
  code: number;

  @ApiProperty({
    enum: LogResponseStatusEnum,
    example: LogResponseStatusEnum.SUCCESS,
  })
  logStatus: LogResponseStatusEnum;

  @ApiProperty({
    enum: ApiResponseStatusEnum,
    example: ApiResponseStatusEnum.SUCCESS,
  })
  status: ApiResponseStatusEnum;

  @ApiProperty({ type: GetUserDetailResult })
  data: GetUserDetailResult;
}
