import { ApiProperty } from '@nestjs/swagger';
import {
  ResponseInterface,
  LogResponseStatusEnum,
  ApiResponseStatusEnum,
} from '../../../interceptors/response/response.interface';

export class UpdateUserRo implements ResponseInterface<void> {
  @ApiProperty({ example: 200 })
  code: number;

  @ApiProperty({
    enum: LogResponseStatusEnum,
    example: LogResponseStatusEnum.SUCCESS,
  })
  logStatus: LogResponseStatusEnum;

  @ApiProperty({
    enum: ApiResponseStatusEnum,
    example: ApiResponseStatusEnum.SUCCESS,
  })
  status: ApiResponseStatusEnum;
}
