import { ApiProperty } from '@nestjs/swagger';
import { customApiPropertyDescription } from '../../../utils/swagger';
import {
  ResponseInterface,
  LogResponseStatusEnum,
  ApiResponseStatusEnum,
} from '../../../interceptors/response/response.interface';

export class CreateUserResult {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'ID',
    }),
    example: 1,
  })
  id: number;
}

export class CreateUserRo implements ResponseInterface<CreateUserResult> {
  @ApiProperty({ example: 201 })
  code: number;

  @ApiProperty({
    enum: LogResponseStatusEnum,
    example: LogResponseStatusEnum.SUCCESS,
  })
  logStatus: LogResponseStatusEnum;

  @ApiProperty({
    enum: ApiResponseStatusEnum,
    example: ApiResponseStatusEnum.SUCCESS,
  })
  status: ApiResponseStatusEnum;

  @ApiProperty({ type: CreateUserResult })
  data: CreateUserResult;
}
