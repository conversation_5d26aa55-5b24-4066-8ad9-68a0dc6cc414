import { ApiProperty } from '@nestjs/swagger';
import {
  ApiResponseStatusEnum,
  LogResponseStatusEnum,
  ResponseInterface,
} from '../../../interceptors/response/response.interface';

export class DeleteUserRo implements ResponseInterface<void> {
  @ApiProperty({ example: 200 })
  code: number;

  @ApiProperty({
    enum: LogResponseStatusEnum,
    example: LogResponseStatusEnum.SUCCESS,
  })
  logStatus: LogResponseStatusEnum;

  @ApiProperty({
    enum: ApiResponseStatusEnum,
    example: ApiResponseStatusEnum.SUCCESS,
  })
  status: ApiResponseStatusEnum;
}
