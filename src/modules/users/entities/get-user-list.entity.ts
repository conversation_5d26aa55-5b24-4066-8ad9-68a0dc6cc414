import { ApiProperty } from '@nestjs/swagger';
import {
  ApiResponseStatusEnum,
  LogResponseStatusEnum,
  ResponseInterface,
} from '../../../interceptors/response/response.interface';
import { UserStatusEnum } from '../../../modules/auth/auth.interface';
import { customApiPropertyDescription } from '../../../utils/swagger';

class UserCreatorOrUpdater {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '名字',
    }),
    example: '名字',
  })
  firstName: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '姓氏',
    }),
    example: '姓氏',
  })
  lastName: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '信箱',
    }),
    example: '<EMAIL>',
  })
  email: string;
}

class UserRole {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'ID',
    }),
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '名稱',
    }),
    example: '名稱',
  })
  name: string;
}

class User {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'ID',
    }),
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '信箱',
    }),
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '名字',
    }),
    example: '名字',
  })
  firstName: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '姓氏',
    }),
    example: '姓氏',
  })
  lastName: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '部門',
    }),
    example: '產品研發部',
  })
  department: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '職稱',
    }),
    example: 'RD',
  })
  title: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '狀態',
      enumTypeDescription: [
        `${UserStatusEnum.UNVERIFIED}：停用`,
        `${UserStatusEnum.VERIFIED}：啟用`,
        `${UserStatusEnum.SUSPENDED}：邀請中`,
      ],
    }),
    enum: UserStatusEnum,
    example: UserStatusEnum.VERIFIED,
  })
  status: UserStatusEnum;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '建立人',
    }),
  })
  creator: UserCreatorOrUpdater;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '建立時間',
    }),
    example: 1622505600000,
  })
  createdAt: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '最後更新人',
    }),
  })
  updater: UserCreatorOrUpdater;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '最後更新時間',
    }),
    example: 1622505600000,
  })
  updatedAt: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '所綁定的角色',
    }),
    type: [UserRole],
  })
  roles: UserRole[];
}

export class GetUserListResult {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '使用者列表',
    }),
    type: [User],
  })
  users: User[];

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '總數量',
    }),
    example: 1,
  })
  totalCount: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '總頁數',
    }),
    example: 1,
  })
  totalPage: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '當前頁數',
    }),
    example: 1,
  })
  currentPage: number;
}

export class GetUserListRo implements ResponseInterface<GetUserListResult> {
  @ApiProperty({ example: 200 })
  code: number;

  @ApiProperty({
    enum: LogResponseStatusEnum,
    example: LogResponseStatusEnum.SUCCESS,
  })
  logStatus: LogResponseStatusEnum;

  @ApiProperty({
    enum: ApiResponseStatusEnum,
    example: ApiResponseStatusEnum.SUCCESS,
  })
  status: ApiResponseStatusEnum;

  @ApiProperty({ type: GetUserListResult })
  data: GetUserListResult;
}
