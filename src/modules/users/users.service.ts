import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, ObjectLiteral, Repository } from 'typeorm';
import * as bcrypt from 'bcrypt';
import { Enforcer } from 'casbin';
import { isEmpty, pick, difference, pickBy } from 'lodash';
import { User, UserStatusEnum } from '../../database/user.entity';
import { CasbinService } from '../casbin/casbin.service';
import { PermissionConfig } from '../permission-object-groups/permission-object-groups.interface';
import { RolesService } from '../roles/roles.service';
import {
  CreateFailedOperationLogReq,
  CreateSuccessfulOperationLogReq,
} from '../operation-logs/operation-logs.interface';
import { OperationLogsService } from '../operation-logs/operation-logs.service';
import {
  AddRolesToUserReq,
  CreateUserReq,
  GetUserDetailRes,
  GetUserListReq,
  GetUserListRes,
  UpdateUserReq,
  IsLastVerifiedUserWithRoleReq,
  UpdateUserStatusReq,
  UserOperationTypeEnum,
  CreateUserRes,
  DeleteUserReq,
  UpdateUserStatusEnum,
  RemoveRolesFromUserReq,
  SetPasswordReq,
  ResetPasswordReq,
  ForgotPasswordReq,
  GenerateSetPasswordTokenReq,
  GenerateResetPasswordTokenReq,
  JwtTokenTypeEnum,
  CreateUserOperationLogRes,
  CreateUserOperationLogReq,
  GetUserMapByEmailsReq,
  GetUserMapByEmailsRes,
  GetUserMapByUserIdsReq,
  GetUserMapByUserIdsRes,
} from './users.interface';
import {
  bigintToNumber,
  getPagingSkip,
  getPagingTake,
} from '../../utils/value-conversion';
import { adminRoleId } from '../../commands/seeds/admin-role.seed';
import { OperationConfig } from '../operation-object-groups/operation-object-groups.interface';
import { MailService } from '../mail/mail.service';
import { UserSetPasswordMail } from '../mail/template/user-set-password/user-set-password-mail';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { UserResetPasswordMail } from '../mail/template/user-reset-password/user-reset-password-mail';
import { UnleashFeatureFlagNameEnum } from '../unleash-feature-flags/unleash-feature-flags.interface';
import { UnleashFeatureFlagsService } from '../unleash-feature-flags/unleash-feature-flags.service';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private readonly configService: ConfigService,
    private readonly casbinService: CasbinService,
    private readonly rolesService: RolesService,
    private readonly mailService: MailService,
    private readonly jwtService: JwtService,
    private readonly operationLogsService: OperationLogsService,
    private unleashFeatureFlagsService: UnleashFeatureFlagsService,
    @Inject('CASBIN_ENFORCER') private enforcer: Enforcer,
  ) {}

  /**
   * @description 新增操作記錄
   */
  public async createUserOperationLog({
    type,
    isSuccess,
    operatorUserId,
    userId,
    userName,
    userOriginalData,
    inputData,
    outputData,
    error,
  }: CreateUserOperationLogReq): Promise<CreateUserOperationLogRes> {
    const successfulOrFailedOperationLogs:
      | CreateSuccessfulOperationLogReq[]
      | CreateFailedOperationLogReq[] = [];
    const failedOperationLogs: CreateFailedOperationLogReq[] = [];

    const targetId = userId?.toString();
    const targetName = userName;
    const targetOriginalData = userOriginalData;
    const creatorUserId = operatorUserId;

    switch (type) {
      case UserOperationTypeEnum.GET_USER_LIST: {
        const {
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
        } =
          OperationConfig.AUTHORIZATION_MANAGEMENT.USER_MANAGEMENT
            .GET_USER_LIST;
        successfulOrFailedOperationLogs.push({
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
          inputData,
          outputData,
          creatorUserId,
        });
        break;
      }
      case UserOperationTypeEnum.CREATE_USER: {
        const {
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
        } =
          OperationConfig.AUTHORIZATION_MANAGEMENT.USER_MANAGEMENT.CREATE_USER;
        successfulOrFailedOperationLogs.push({
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
          targetId,
          targetName,
          inputData,
          outputData,
          creatorUserId,
        });
        break;
      }
      case UserOperationTypeEnum.GET_USER_DETAIL: {
        const {
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
        } =
          OperationConfig.AUTHORIZATION_MANAGEMENT.USER_MANAGEMENT
            .GET_USER_DETAIL;
        successfulOrFailedOperationLogs.push({
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
          targetId,
          targetName,
          inputData,
          outputData,
          creatorUserId,
        });
        break;
      }
      case UserOperationTypeEnum.UPDATE_USER: {
        const operations = [];
        switch (inputData.status as UpdateUserStatusEnum) {
          case UpdateUserStatusEnum.ENABLE:
            operations.push(
              OperationConfig.AUTHORIZATION_MANAGEMENT.USER_MANAGEMENT
                .UPDATE_USER.ENABLE,
            );
            break;

          case UpdateUserStatusEnum.DISABLE:
            operations.push(
              OperationConfig.AUTHORIZATION_MANAGEMENT.USER_MANAGEMENT
                .UPDATE_USER.DISABLE,
            );
            break;
          default:
            operations.push(
              OperationConfig.AUTHORIZATION_MANAGEMENT.USER_MANAGEMENT
                .UPDATE_USER.UPDATE_INFO,
            );
            break;
        }
        if (inputData.status) {
          const isUpdateInfo = Object.keys(
            pickBy(
              inputData,
              (value, key: string) => key !== 'status' && value,
            ),
          ).length;
          if (isUpdateInfo) {
            operations.push(
              OperationConfig.AUTHORIZATION_MANAGEMENT.USER_MANAGEMENT
                .UPDATE_USER.UPDATE_INFO,
            );
          }
        }
        operations.forEach(
          ({
            operationObjectGroupKey,
            operationObjectKey,
            operationKey,
            operationEventKey,
            targetType,
          }) => {
            successfulOrFailedOperationLogs.push({
              operationObjectGroupKey,
              operationObjectKey,
              operationKey,
              operationEventKey,
              targetType,
              targetId,
              targetName,
              targetOriginalData,
              inputData,
              creatorUserId,
            });
          },
        );
        break;
      }
      case UserOperationTypeEnum.DELETE_USER: {
        const {
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
        } =
          OperationConfig.AUTHORIZATION_MANAGEMENT.USER_MANAGEMENT.DELETE_USER;
        successfulOrFailedOperationLogs.push({
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
          targetId,
          targetName,
          inputData,
          creatorUserId,
        });
        break;
      }
      default:
        break;
    }

    if (isSuccess) {
      return this.operationLogsService.createSuccessfulOperationLogs(
        successfulOrFailedOperationLogs,
      );
    }

    successfulOrFailedOperationLogs.forEach(
      (successfulOrFailedOperationLog) => {
        failedOperationLogs.push({
          ...successfulOrFailedOperationLog,
          failCode: error?.code || null,
          failReason: error?.message || error,
        });
      },
    );
    return this.operationLogsService.createFailedOperationLogs(
      failedOperationLogs,
    );
  }

  /**
   * @description 檢查是否有檢視的權限
   */
  public async checkAuthUserReadPermission(
    operatorUserId: number,
  ): Promise<void> {
    await this.casbinService.checkPermission(
      operatorUserId,
      PermissionConfig.AUTHORIZATION_MANAGEMENT.USER_MANAGEMENT.READ,
    );
  }

  /**
   * @description 取得使用者列表
   */
  public async getUserList({
    keyword,
    roleId,
    status,
    size,
    page,
  }: GetUserListReq): Promise<GetUserListRes> {
    const whereConditions: string[] = [];
    const parameters: ObjectLiteral = {};

    if (keyword) {
      whereConditions.push(
        '(user.email ILike :keyword OR CONCAT( user.lastName, user.firstName ) ILike :keyword)',
      );
      parameters.keyword = `%${keyword}%`;
    }

    if (roleId) {
      const checkRoleIdsRes = await this.rolesService.checkRoleIds([roleId]);
      if (!checkRoleIdsRes) throw new Error('Contains invalid roleId(s).');

      const userIds = await this.casbinService.getUserIdsByRoleId(roleId);
      if (!userIds.length) {
        return {
          users: [],
          totalCount: 0,
        };
      }

      whereConditions.push('user.id IN (:...userIds)');
      parameters.userIds = userIds;
    }

    if (status) {
      whereConditions.push('user.status = :status');
      parameters.status = status;
    }

    const queryBuilder = this.userRepository
      .createQueryBuilder('user')
      .withDeleted()
      .select([
        'user.id',
        'user.email',
        'user.firstName',
        'user.lastName',
        'user.department',
        'user.title',
        'user.status',
        'user.passwordLoginAllowed',
        'user.createdAt',
        'user.updatedAt',
      ])
      .leftJoin('user.creator', 'creator')
      .addSelect(['creator.firstName', 'creator.lastName', 'creator.email'])
      .leftJoin('user.updater', 'updater')
      .addSelect(['updater.firstName', 'updater.lastName', 'updater.email'])
      .where('user.deleted_at IS NULL')
      .orderBy('user.createdAt', 'DESC');

    if (!isEmpty(whereConditions) && !isEmpty(parameters)) {
      whereConditions.forEach((whereCondition) => {
        queryBuilder.andWhere(whereCondition);
      });
      queryBuilder.setParameters(parameters);
    }

    if (size && page) {
      queryBuilder.skip(getPagingSkip(size, page)).take(getPagingTake(size));
    }

    const [users, totalCount] = await queryBuilder.getManyAndCount();

    return {
      users: await Promise.all(
        users.map(async (user) => ({
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          department: user.department,
          title: user.title,
          email: user.email,
          status: user.status,
          passwordLoginAllowed: user.passwordLoginAllowed || false,
          creator: user.creator
            ? {
                firstName: user.creator.firstName,
                lastName: user.creator.lastName,
                email: user.creator.email,
              }
            : null,
          createdAt: bigintToNumber(user.createdAt),
          updater: user.updater
            ? {
                firstName: user.updater.firstName,
                lastName: user.updater.lastName,
                email: user.updater.email,
              }
            : null,
          updatedAt: bigintToNumber(user.updatedAt),
          roles: (await this.rolesService.getRoleListByUserId(user.id)).roles,
        })),
      ),
      totalCount,
    };
  }

  /**
   * @description 檢查是否有新增的權限
   */
  public async checkAuthUserCreatePermission(
    operatorUserId: number,
  ): Promise<void> {
    await this.casbinService.checkPermission(
      operatorUserId,
      PermissionConfig.AUTHORIZATION_MANAGEMENT.USER_MANAGEMENT.CREATE,
    );
  }

  /**
   * @description 使用者綁定角色
   * @access private
   */
  private async addRolesToUser({
    userId,
    roleIds,
  }: AddRolesToUserReq): Promise<void> {
    await Promise.all(
      roleIds.map((roleId) => {
        this.enforcer.addRoleForUser(`user::${userId}`, `role::${roleId}`);
      }),
    );
  }

  private async generateAndSaveSetPasswordToken({
    userId,
    email,
  }: GenerateSetPasswordTokenReq): Promise<string> {
    const setPasswordToken = this.jwtService.sign(
      {
        type: JwtTokenTypeEnum.SET_PASSWORD,
        userId,
        email,
      },
      {
        expiresIn: this.configService.get(
          'sendMail.setPassword.tokenExpiresIn',
        ),
        secret: this.configService.get('sendMail.setPassword.tokenSecret'),
      },
    );
    await this.userRepository.update(userId, {
      setPasswordToken,
    });

    return setPasswordToken;
  }

  private async generateAndSaveResetPasswordToken({
    userId,
    email,
  }: GenerateResetPasswordTokenReq): Promise<string> {
    const resetPasswordToken = this.jwtService.sign(
      {
        type: JwtTokenTypeEnum.RESET_PASSWORD,
        userId,
        email,
      },
      {
        expiresIn: this.configService.get(
          'sendMail.resetPassword.tokenExpiresIn',
        ),
        secret: this.configService.get('sendMail.resetPassword.tokenSecret'),
      },
    );
    await this.userRepository.update(userId, {
      resetPasswordToken,
    });

    return resetPasswordToken;
  }

  /**
   * @description 新增使用者，並且綁定角色
   */
  public async createUser({
    operatorUserId,
    createInfo,
  }: CreateUserReq): Promise<CreateUserRes> {
    const checkRoleIdsRes = await this.rolesService.checkRoleIds(
      createInfo.roleIds,
    );
    if (!checkRoleIdsRes) throw new Error('Contains invalid roleId(s).');

    const { id: createdUserId } = await this.userRepository.save({
      email: createInfo.email.toLowerCase(),
      firstName: createInfo.firstName,
      lastName: createInfo.lastName,
      department: createInfo.department,
      title: createInfo.title,
      status: UserStatusEnum.UNVERIFIED,
      passwordLoginAllowed: createInfo.passwordLoginAllowed,
      creatorUserId: operatorUserId,
      updaterUserId: operatorUserId,
    });

    await this.addRolesToUser({
      userId: createdUserId,
      roleIds: createInfo.roleIds,
    });

    if (createInfo.passwordLoginAllowed) {
      const setPasswordToken = await this.generateAndSaveSetPasswordToken({
        userId: createdUserId,
        email: createInfo.email,
      });
      const url = new URL(
        this.configService.get('sendMail.setPassword.pageUrl'),
      );
      url.searchParams.append('token', setPasswordToken);
      await this.mailService.sendMail({
        to: createInfo.email,
        mailTemplate: new UserSetPasswordMail(
          this.configService.get('sendMail.logoUrl'),
          url.toString(),
        ),
      });
    }

    return {
      id: createdUserId,
    };
  }

  /**
   * @description 取得使用者詳細資訊，無法撈到已經刪除的使用者
   */
  public async getUserDetail(userId: number): Promise<GetUserDetailRes> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });
    if (!user || user.deleterUserId) throw new Error('User not found.');
    const { roles, roleIds } =
      await this.rolesService.getRoleListByUserId(userId);
    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      department: user.department,
      title: user.title,
      status: user.status,
      passwordLoginAllowed: user.passwordLoginAllowed || false,
      roleIds,
      roles,
    };
  }

  /**
   * @description 檢查是否有編輯的權限
   */
  public async checkAuthUserUpdatePermission(
    operatorUserId: number,
  ): Promise<void> {
    await this.casbinService.checkPermission(
      operatorUserId,
      PermissionConfig.AUTHORIZATION_MANAGEMENT.USER_MANAGEMENT.UPDATE,
    );
  }

  /**
   * @description 判斷使用者是否為此角色最後一個
   */
  private async isLastVerifiedUserWithRole({
    userId,
    roleId,
  }: IsLastVerifiedUserWithRoleReq): Promise<boolean> {
    const userIds = (
      await this.enforcer.getUsersForRole(`role::${roleId}`)
    ).map((user) => parseInt(user.split('::')[1], 10));

    const verifiedUsers = await this.userRepository.find({
      where: {
        id: In(userIds),
        status: UserStatusEnum.VERIFIED,
      },
    });
    const verifiedUserIds = verifiedUsers.map(({ id }) => id);

    return verifiedUserIds.length === 1 && verifiedUserIds[0] === userId;
  }

  /**
   * @description 移除使用者綁定角色
   * @access private
   */
  private async removeRolesFromUser({
    userId,
    roleIds,
  }: RemoveRolesFromUserReq) {
    await Promise.all(
      roleIds.map((roleId) =>
        this.enforcer.deleteRoleForUser(`user::${userId}`, `role::${roleId}`),
      ),
    );
  }

  /**
   * @description 編輯使用者
   */
  public async updateUser({
    operatorUserId,
    targetUserId,
    targetUserInfo,
    updateInfo,
  }: UpdateUserReq): Promise<void> {
    if (operatorUserId === targetUserId) {
      throw new Error("Can't update yourself.");
    }
    const { roleIds, status } = updateInfo;
    if (roleIds) {
      const checkRoleIdsRes = await this.rolesService.checkRoleIds(roleIds);
      if (!checkRoleIdsRes) throw new Error('Contains invalid roleId(s).');
      const roleIdsToAppend = difference(roleIds, targetUserInfo.roleIds);
      const roleIdsToRemove = difference(targetUserInfo.roleIds, roleIds);
      if (
        roleIdsToRemove.includes(adminRoleId) &&
        (await this.isLastVerifiedUserWithRole({
          userId: targetUserId,
          roleId: adminRoleId,
        }))
      ) {
        throw new Error(
          "Can't update the user role because the system admin role of the user that is the last one user.",
        );
      }

      if (roleIdsToRemove.length) {
        await this.removeRolesFromUser({
          userId: targetUserId,
          roleIds: roleIdsToRemove,
        });
      }

      if (roleIdsToAppend.length) {
        await this.addRolesToUser({
          userId: targetUserId,
          roleIds: roleIdsToAppend,
        });
      }
    } else if (status === UpdateUserStatusEnum.DISABLE) {
      if (
        await this.isLastVerifiedUserWithRole({
          userId: targetUserId,
          roleId: adminRoleId,
        })
      ) {
        throw new Error(
          "Can't disable the user role because the system admin role of the user that is the last one user.",
        );
      }

      await this.removeRolesFromUser({
        userId: targetUserId,
        roleIds: targetUserInfo.roleIds,
      });
    }

    const pickUpdateInfo: Partial<User> = pick(
      {
        ...updateInfo,
        status:
          updateInfo.status === UpdateUserStatusEnum.ENABLE
            ? UserStatusEnum.UNVERIFIED
            : updateInfo.status === UpdateUserStatusEnum.DISABLE
              ? UserStatusEnum.SUSPENDED
              : undefined,
      },
      ['email', 'firstName', 'lastName', 'department', 'title', 'status'],
    );
    await this.userRepository.update(targetUserId, {
      ...pickUpdateInfo,
      updaterUserId: operatorUserId,
      updatedAt: BigInt(new Date().getTime()),
    });
  }

  /**
   * @description 檢查是否有刪除的權限
   */
  public async checkAuthUserDeletePermission(
    operatorUserId: number,
  ): Promise<void> {
    await this.casbinService.checkPermission(
      operatorUserId,
      PermissionConfig.AUTHORIZATION_MANAGEMENT.USER_MANAGEMENT.DELETE,
    );
  }

  /**
   * @description 刪除使用者（啟用的使用者無法刪除）
   */
  public async deleteUser({
    operatorUserId,
    targetUserId,
    targetUserInfo,
  }: DeleteUserReq): Promise<void> {
    if (operatorUserId === targetUserId) {
      throw new Error("Can't delete yourself.");
    }

    if (targetUserInfo.status === UserStatusEnum.VERIFIED) {
      throw new Error("Can't delete the user that status is verified.");
    }

    if (
      targetUserInfo.roleIds.includes(adminRoleId) &&
      (await this.isLastVerifiedUserWithRole({
        userId: targetUserId,
        roleId: adminRoleId,
      }))
    ) {
      throw new Error(
        "Can't delete the user role because the system admin role of the user that is the last one user.",
      );
    }

    await this.userRepository.update(targetUserId, {
      status: UserStatusEnum.SUSPENDED,
      deleterUserId: operatorUserId,
      deletedAt: BigInt(new Date().getTime()),
    });

    await this.enforcer.deleteUser(`user::${targetUserId}`);
  }

  /**
   * @description 使用 Email 查詢使用者資訊
   */
  public async getUserByEmail(email: string): Promise<User | null> {
    return (
      await this.userRepository
        .createQueryBuilder('user')
        .where('user.deleted_at IS NULL')
        .andWhere('user.email = :email', { email })
        .getMany()
    )[0];
  }

  /**
   * @description 更新使用者狀態
   */
  public async updateUserStatus({
    id,
    status,
    updatedAt,
  }: UpdateUserStatusReq): Promise<void> {
    await this.userRepository.update(id, {
      status: UserStatusEnum[status],
      updatedAt,
    });
  }

  public async resendInvitation(targetUserId: number) {
    const isEnabled = await this.unleashFeatureFlagsService.isEnabled(
      UnleashFeatureFlagNameEnum.DISABLE_PASSWORD_LOGIN,
    );
    if (isEnabled) throw new Error('This feature is not available.');

    const user = await this.userRepository.findOne({
      where: { id: targetUserId },
    });
    if (!user || user.deleterUserId) {
      throw new Error('This account does not exist.');
    }

    if (
      user.status !== UserStatusEnum.UNVERIFIED ||
      !user.passwordLoginAllowed
    ) {
      throw new Error("This account can't resend invitation.");
    }

    const setPasswordToken = await this.generateAndSaveSetPasswordToken({
      userId: user.id,
      email: user.email,
    });
    const url = new URL(this.configService.get('sendMail.setPassword.pageUrl'));
    url.searchParams.append('token', setPasswordToken);
    await this.mailService.sendMail({
      to: user.email,
      mailTemplate: new UserSetPasswordMail(
        this.configService.get('sendMail.logoUrl'),
        url.toString(),
      ),
    });
  }

  /**
   * @description 設定使用者密碼，並且更新使用者狀態
   */
  public async setPassword({ userId, password }: SetPasswordReq) {
    const isEnabled = await this.unleashFeatureFlagsService.isEnabled(
      UnleashFeatureFlagNameEnum.DISABLE_PASSWORD_LOGIN,
    );
    if (isEnabled) throw new Error('This feature is not available.');

    return this.userRepository.update(userId, {
      status: UserStatusEnum.VERIFIED,
      password: await bcrypt.hash(password, 10),
      setPasswordToken: null,
    });
  }

  /**
   * @description 重設使用者密碼，並且更新使用者狀態
   */
  public async resetPassword({ userId, password }: ResetPasswordReq) {
    const isEnabled = await this.unleashFeatureFlagsService.isEnabled(
      UnleashFeatureFlagNameEnum.DISABLE_PASSWORD_LOGIN,
    );
    if (isEnabled) throw new Error('This feature is not available.');

    return this.userRepository.update(userId, {
      password: await bcrypt.hash(password, 10),
      resetPasswordToken: null,
    });
  }

  /**
   * @description 忘記密碼重新寄送重設密碼信件
   */
  public async forgotPassword({ email }: ForgotPasswordReq) {
    const isEnabled = await this.unleashFeatureFlagsService.isEnabled(
      UnleashFeatureFlagNameEnum.DISABLE_PASSWORD_LOGIN,
    );
    if (isEnabled) throw new Error('This feature is not available.');

    const user = await this.userRepository.findOne({
      where: { email },
    });

    if (!user || user.deleterUserId) {
      throw new Error('This account does not exist.');
    }
    switch (user.status) {
      case UserStatusEnum.UNVERIFIED:
        throw new Error(
          "This account's user status is unverified, so the reset password email won't be sent.",
        );
      case UserStatusEnum.SUSPENDED:
        throw new Error(
          "This account's user status is suspended, so the reset password email won't be sent.",
        );
    }

    if (!user.passwordLoginAllowed) {
      throw new Error(
        "This account doesn't allow password logins, so the reset password email won't be sent.",
      );
    }

    const resetPasswordToken = await this.generateAndSaveResetPasswordToken({
      userId: user.id,
      email: user.email,
    });
    const url = new URL(
      this.configService.get('sendMail.resetPassword.pageUrl'),
    );
    url.searchParams.append('token', resetPasswordToken);
    await this.mailService.sendMail({
      to: user.email,
      mailTemplate: new UserResetPasswordMail(
        this.configService.get('sendMail.logoUrl'),
        url.toString(),
      ),
    });
  }

  /**
   * @description 使用 Emails 查詢使用者資訊
   */
  public async getUserMapByEmails({
    emails,
  }: GetUserMapByEmailsReq): Promise<GetUserMapByEmailsRes> {
    const uniqueEmails = [...new Set(emails.filter((email) => email))];

    const userDetails = await Promise.all(
      uniqueEmails.map(async (email) => {
        return await this.userRepository
          .createQueryBuilder('user')
          .where('user.email = :email', { email })
          .orderBy('user.deleted_at IS NULL', 'DESC')
          .addOrderBy('user.deleted_at', 'DESC')
          .getOne();
      }),
    );

    const userMap = new Map<
      string,
      {
        firstName: string;
        lastName: string;
      }
    >();
    userDetails?.forEach((user) => {
      if (user) {
        const { email, firstName, lastName } = user;
        userMap.set(email, {
          firstName,
          lastName,
        });
      }
    });

    return userMap;
  }

  /**
   * @description 使用 User IDs 查詢使用者資訊
   */
  public async getUserMapByUserIds({
    userIds,
  }: GetUserMapByUserIdsReq): Promise<GetUserMapByUserIdsRes> {
    const uniqueUserIds = [...new Set(userIds.filter((id) => id > 0))];

    const userDetails = await Promise.all(
      uniqueUserIds?.map(
        async (userId) =>
          await this.userRepository.findOne({ where: { id: userId } }),
      ),
    );

    const userMap = new Map<
      number,
      {
        firstName: string;
        lastName: string;
        email: string;
      }
    >();
    userDetails?.forEach((user) => {
      if (user) {
        const { id, firstName, lastName, email } = user;
        userMap.set(id, { firstName, lastName, email });
      }
    });

    return userMap;
  }
}
