import { Module } from '@nestjs/common';
import { UsersService } from './users.service';
import { UsersController } from './users.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../../database/user.entity';
import { UsersExceptionFilter } from './filters/users-exception.filter';
import { UsersResponseFilter } from './filters/users-response.filter';
import { CasbinModule } from '../casbin/casbin.module';
import { OperationLogsModule } from '../operation-logs/operation-logs.module';
import { RolesModule } from '../roles/roles.module';
import { MailModule } from '../mail/mail.module';
import { JwtModule } from '@nestjs/jwt';
import { UnleashFeatureFlagsModule } from '../unleash-feature-flags/unleash-feature-flags.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
    CasbinModule,
    OperationLogsModule,
    RolesModule,
    MailModule,
    JwtModule,
    UnleashFeatureFlagsModule,
  ],
  controllers: [UsersController],
  providers: [UsersService, UsersExceptionFilter, UsersResponseFilter],
  exports: [UsersService],
})
export class UsersModule {}
