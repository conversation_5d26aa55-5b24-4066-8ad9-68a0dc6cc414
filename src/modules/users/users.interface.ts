import { UserStatusEnum } from '../../database/user.entity';
import { CreateOperationLogRes } from '../operation-logs/operation-logs.interface';

export enum UserOperationTypeEnum {
  CREATE_USER = 'CREATE_USER',
  GET_USER_LIST = 'GET_USER_LIST',
  GET_USER_DETAIL = 'GET_USER_DETAIL',
  UPDATE_USER = 'UPDATE_USER',
  DELETE_USER = 'DELETE_USER',
}

export enum UpdateUserStatusEnum {
  ENABLE = 'ENABLE',
  DISABLE = 'DISABLE',
}

export enum JwtTokenTypeEnum {
  RESET_PASSWORD = 'RESET_PASSWORD',
  SET_PASSWORD = 'SET_PASSWORD',
}

export interface CreateUserOperationLogReq {
  type: UserOperationTypeEnum;
  isSuccess: boolean;
  operatorUserId: number;
  userId?: number;
  userName?: string;
  userOriginalData?: Record<string, any>;
  inputData?: Record<string, any>;
  outputData?: Record<string, any>;
  error?: any;
}
export interface CreateUserOperationLogRes extends CreateOperationLogRes {}

export interface GetUserListReq {
  keyword?: string;
  roleId?: number;
  status?: UserStatusEnum;
  size?: number;
  page?: number;
}

interface UserCreatorOrUpdater {
  firstName: string;
  lastName: string;
  email: string;
}

export interface GetUserListRes {
  users: {
    id: number;
    firstName: string;
    lastName: string;
    department: string;
    title: string;
    email: string;
    status: UserStatusEnum;
    passwordLoginAllowed: boolean;
    creator: UserCreatorOrUpdater;
    createdAt: number;
    updater: UserCreatorOrUpdater;
    updatedAt: number;
    roles: { id: number; name: string }[];
  }[];
  totalCount: number;
}

export interface AddRolesToUserReq {
  userId: number;
  roleIds: number[];
}

export interface GenerateSetPasswordTokenReq {
  userId: number;
  email: string;
}

export interface GenerateResetPasswordTokenReq {
  userId: number;
  email: string;
}

export interface CreateUserReq {
  operatorUserId: number;
  createInfo: {
    department: string;
    title: string;
    lastName: string;
    firstName: string;
    email: string;
    passwordLoginAllowed: boolean;
    roleIds: number[];
  };
}

export interface CreateUserRes {
  id: number;
}

export interface GetUserDetailRes {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  department: string;
  title: string;
  status: UserStatusEnum;
  passwordLoginAllowed: boolean;
  roles: { id: number; name: string }[];
  roleIds: number[];
}

export interface IsLastVerifiedUserWithRoleReq {
  userId: number;
  roleId: number;
}

export interface RemoveRolesFromUserReq {
  userId: number;
  roleIds: number[];
}

export interface UpdateUserReq {
  operatorUserId: number;
  targetUserId: number;
  targetUserInfo: GetUserDetailRes;
  updateInfo: {
    department?: string;
    title?: string;
    lastName?: string;
    firstName?: string;
    email?: string;
    roleIds?: number[];
    status?: UpdateUserStatusEnum;
  };
}
export interface DeleteUserReq {
  operatorUserId: number;
  targetUserId: number;
  targetUserInfo: GetUserDetailRes;
}

export interface UpdateUserStatusReq {
  id: number;
  status: UserStatusEnum;
  updatedAt: bigint | (() => string);
}

export interface SetPasswordReq {
  userId: number;
  password: string;
}

export interface ResetPasswordReq {
  userId: number;
  password: string;
}

export interface ForgotPasswordReq {
  email: string;
}

export interface GetUserMapByEmailsReq {
  emails: string[];
}
export type GetUserMapByEmailsRes = Map<
  string,
  {
    firstName: string;
    lastName: string;
  }
>;

export interface GetUserMapByUserIdsReq {
  userIds: number[];
}
export type GetUserMapByUserIdsRes = Map<
  number,
  {
    firstName: string;
    lastName: string;
    email: string;
  }
>;
