import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  UseGuards,
  Req,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiCreatedResponse,
  ApiOkResponse,
  ApiOperation,
} from '@nestjs/swagger';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { CreateUserResult, CreateUserRo } from './entities/create-user.entity';
import {
  GetUserDetailResult,
  GetUserDetailRo,
} from './entities/get-user-detail.entity';
import {
  GetUserListResult,
  GetUserListRo,
} from './entities/get-user-list.entity';
import UserAuthGuard from '../auth/guards/user-auth.guard';
import { UsersExceptionFilter } from './filters/users-exception.filter';
import { UsersResponseFilter } from './filters/users-response.filter';
import { UsersResponseFilterTypeEnum } from './filters/users.filter.interface';
import { ApiResultAndCreateOperationLogResult } from '../../interceptors/response/response.interface';
import { GetUserListDto } from './dto/get-user-list.dto';
import { DeleteUserRo } from './entities/delete-user.entity';
import {
  CreateUserOperationLogReq,
  UserOperationTypeEnum,
} from './users.interface';
import { UpdateUserRo } from './entities/update-user.entity';
import { getFullName } from '../../utils/value-conversion';
import { customApiOperationDescription } from '../../utils/swagger';

@ApiBearerAuth()
@UseGuards(UserAuthGuard)
@Controller('users')
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly usersExceptionFilter: UsersExceptionFilter,
    private readonly usersResponseFilter: UsersResponseFilter,
  ) {}

  @Get()
  @ApiOperation({
    summary: '取得使用者列表',
    description: customApiOperationDescription({
      permissions: ['權限管理 > 使用者設定 > 檢視'],
      operationLogs: ['權限管理 > 使用者設定 > 查詢使用者清單'],
    }),
  })
  @ApiOkResponse({
    type: GetUserListRo,
  })
  async getUserList(
    @Req() req,
    @Query() getUserListDto: GetUserListDto,
  ): Promise<ApiResultAndCreateOperationLogResult<GetUserListResult>> {
    const { userId: operatorUserId } = req.user;
    const createUserOperationLogReq: CreateUserOperationLogReq = {
      isSuccess: true,
      type: UserOperationTypeEnum.GET_USER_LIST,
      operatorUserId,
      inputData: getUserListDto,
    };
    try {
      await this.usersService.checkAuthUserReadPermission(operatorUserId);
      const res = await this.usersService.getUserList(getUserListDto);
      const result = this.usersResponseFilter.getResponse({
        type: UsersResponseFilterTypeEnum.GET_USER_LIST,
        payload: {
          ...res,
          size: getUserListDto.size,
          page: getUserListDto.page,
        },
      }) as GetUserListResult;
      const operationLogResult = await this.usersService.createUserOperationLog(
        {
          ...createUserOperationLogReq,
          outputData: result,
        },
      );
      return {
        result,
        operationLogResult,
      };
    } catch (error) {
      const operationLogResult = await this.usersService.createUserOperationLog(
        {
          ...createUserOperationLogReq,
          isSuccess: false,
          error,
        },
      );
      throw this.usersExceptionFilter.getResponse(error, operationLogResult);
    }
  }

  @Post()
  @HttpCode(201)
  @ApiOperation({
    summary: '新增使用者',
    description: customApiOperationDescription({
      permissions: ['權限管理 > 使用者設定 > 新增'],
      operationLogs: ['權限管理 > 使用者設定 > 新增使用者'],
    }),
  })
  @ApiBody({
    type: CreateUserDto,
  })
  @ApiCreatedResponse({
    type: CreateUserRo,
  })
  async createUser(
    @Req() req,
    @Body() createUserDto: CreateUserDto,
  ): Promise<ApiResultAndCreateOperationLogResult<CreateUserResult>> {
    const { userId: operatorUserId } = req.user;
    const createUserOperationLogReq: CreateUserOperationLogReq = {
      isSuccess: true,
      type: UserOperationTypeEnum.CREATE_USER,
      operatorUserId,
      userName: getFullName(createUserDto.firstName, createUserDto.lastName),
      inputData: createUserDto,
    };
    try {
      await this.usersService.checkAuthUserCreatePermission(operatorUserId);
      const { id: createdUserId } = await this.usersService.createUser({
        operatorUserId,
        createInfo: createUserDto,
      });
      createUserOperationLogReq.userId = createdUserId;
      const result = this.usersResponseFilter.getResponse({
        type: UsersResponseFilterTypeEnum.CREATE_USER,
        payload: {
          id: createdUserId,
        },
      }) as CreateUserResult;
      const operationLogResult = await this.usersService.createUserOperationLog(
        {
          ...createUserOperationLogReq,
          outputData: result,
        },
      );
      return {
        result,
        operationLogResult,
      };
    } catch (error) {
      const operationLogResult = await this.usersService.createUserOperationLog(
        {
          ...createUserOperationLogReq,
          isSuccess: false,
          error,
        },
      );
      throw this.usersExceptionFilter.getResponse(error, operationLogResult);
    }
  }

  @Get(':userId')
  @ApiOperation({
    summary: '取得使用者詳細資訊',
    description: customApiOperationDescription({
      permissions: ['權限管理 > 使用者設定 > 檢視'],
      operationLogs: ['權限管理 > 使用者設定 > 查詢使用者'],
    }),
  })
  @ApiOkResponse({
    type: GetUserDetailRo,
  })
  async getUserDetail(
    @Req() req,
    @Param('userId', ParseIntPipe) targetUserId: number,
  ): Promise<ApiResultAndCreateOperationLogResult<GetUserDetailResult>> {
    const { userId: operatorUserId } = req.user;
    const createUserOperationLogReq: CreateUserOperationLogReq = {
      isSuccess: true,
      type: UserOperationTypeEnum.GET_USER_DETAIL,
      userId: targetUserId,
      operatorUserId,
    };
    try {
      await this.usersService.checkAuthUserReadPermission(operatorUserId);
      const res = await this.usersService.getUserDetail(targetUserId);
      createUserOperationLogReq.userName = getFullName(
        res.firstName,
        res.lastName,
      );
      const result = this.usersResponseFilter.getResponse({
        type: UsersResponseFilterTypeEnum.GET_USER_DETAIL,
        payload: res,
      }) as GetUserDetailResult;
      const operationLogResult = await this.usersService.createUserOperationLog(
        {
          ...createUserOperationLogReq,
          outputData: result,
        },
      );
      return {
        result,
        operationLogResult,
      };
    } catch (error) {
      const operationLogResult = await this.usersService.createUserOperationLog(
        {
          ...createUserOperationLogReq,
          isSuccess: false,
          error,
        },
      );
      throw this.usersExceptionFilter.getResponse(error, operationLogResult);
    }
  }

  @Patch(':userId')
  @ApiOperation({
    summary: '編輯使用者',
    description: customApiOperationDescription({
      permissions: ['權限管理 > 使用者設定 > 編輯'],
      operationLogs: [
        '權限管理 > 使用者設定 > 編輯使用者（更改資訊）',
        '權限管理 > 使用者設定 > 編輯使用者（啟用）',
        '權限管理 > 使用者設定 > 編輯使用者（停用）',
      ],
    }),
  })
  @ApiBody({
    type: UpdateUserDto,
  })
  @ApiOkResponse({
    type: UpdateUserRo,
  })
  async updateUser(
    @Req() req,
    @Param('userId', ParseIntPipe) targetUserId: number,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<ApiResultAndCreateOperationLogResult<void>> {
    const { userId: operatorUserId } = req.user;
    const createUserOperationLogReq: CreateUserOperationLogReq = {
      isSuccess: true,
      type: UserOperationTypeEnum.UPDATE_USER,
      inputData: updateUserDto,
      operatorUserId,
      userId: targetUserId,
    };
    try {
      await this.usersService.checkAuthUserUpdatePermission(operatorUserId);
      const userInfo = await this.usersService.getUserDetail(targetUserId);
      createUserOperationLogReq.userName = getFullName(
        userInfo.firstName,
        userInfo.lastName,
      );
      createUserOperationLogReq.userOriginalData = userInfo;
      await this.usersService.updateUser({
        operatorUserId,
        targetUserId,
        targetUserInfo: userInfo,
        updateInfo: updateUserDto,
      });
      const operationLogResult = await this.usersService.createUserOperationLog(
        createUserOperationLogReq,
      );
      return {
        operationLogResult,
      };
    } catch (error) {
      const operationLogResult = await this.usersService.createUserOperationLog(
        {
          ...createUserOperationLogReq,
          isSuccess: false,
          error,
        },
      );
      throw this.usersExceptionFilter.getResponse(error, operationLogResult);
    }
  }

  @Delete(':userId')
  @ApiOperation({
    summary: '刪除使用者',
    description: customApiOperationDescription({
      permissions: ['權限管理 > 使用者設定 > 刪除'],
      operationLogs: ['權限管理 > 使用者設定 > 刪除使用者'],
    }),
  })
  @ApiOkResponse({
    type: DeleteUserRo,
  })
  async deleteUser(
    @Req() req,
    @Param('userId', ParseIntPipe) targetUserId: number,
  ): Promise<ApiResultAndCreateOperationLogResult<void>> {
    const { userId: operatorUserId } = req.user;
    const createUserOperationLogReq: CreateUserOperationLogReq = {
      isSuccess: true,
      type: UserOperationTypeEnum.DELETE_USER,
      userId: targetUserId,
      operatorUserId,
    };
    try {
      await this.usersService.checkAuthUserDeletePermission(operatorUserId);
      const userInfo = await this.usersService.getUserDetail(targetUserId);
      createUserOperationLogReq.userName = getFullName(
        userInfo.firstName,
        userInfo.lastName,
      );
      await this.usersService.deleteUser({
        operatorUserId,
        targetUserId,
        targetUserInfo: userInfo,
      });
      const operationLogResult = await this.usersService.createUserOperationLog(
        createUserOperationLogReq,
      );
      return {
        operationLogResult,
      };
    } catch (error) {
      const operationLogResult = await this.usersService.createUserOperationLog(
        {
          ...createUserOperationLogReq,
          isSuccess: false,
          error,
        },
      );
      throw this.usersExceptionFilter.getResponse(error, operationLogResult);
    }
  }
}
