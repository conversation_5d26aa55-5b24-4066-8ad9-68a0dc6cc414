import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsEmail,
  IsInt,
  ArrayUnique,
  ArrayMinSize,
  IsBoolean,
} from 'class-validator';
import { customApiPropertyDescription } from '../../../utils/swagger';
import { IsNotBlank } from '../../../decorators/validations/IsNotBlank';

export class CreateUserDto {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '部門',
    }),
    example: '產品研發部',
  })
  @IsString()
  @IsNotBlank()
  readonly department: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '職稱',
    }),
    example: 'RD',
  })
  @IsString()
  @IsNotBlank()
  readonly title: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '姓氏',
    }),
    example: '姓氏',
  })
  @IsString()
  @IsNotBlank()
  readonly lastName: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '名字',
    }),
    example: '名字',
  })
  @IsString()
  @IsNotBlank()
  readonly firstName: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '信箱',
    }),
    example: '<EMAIL>',
  })
  @IsString()
  @IsEmail()
  readonly email: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '允許使用密碼登入',
      remarks: ['預設為 false'],
    }),
    type: Boolean,
    example: false,
  })
  @IsBoolean()
  readonly passwordLoginAllowed: boolean = false;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '角色 IDs',
      limitations: ['至少一個', '不可重複'],
      remarks: ['對應前端「角色」欄位'],
    }),
    type: [Number],
    example: [1, 2],
  })
  @IsInt({ each: true })
  @ArrayMinSize(1)
  @ArrayUnique()
  readonly roleIds: number[];
}
