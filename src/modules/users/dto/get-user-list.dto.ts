import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsOptional, IsString, Min } from 'class-validator';
import { TransformToNumber } from '../../../decorators/transformers/TransformToNumber';
import { UserStatusEnum } from '../../../database/user.entity';
import { customApiPropertyDescription } from '../../../utils/swagger';

export class GetUserListDto {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '角色 ID',
    }),
    required: false,
  })
  @IsOptional()
  @IsInt()
  @TransformToNumber()
  readonly roleId?: number; // IDEA: 可擴充成傳入多個角色 ID

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '狀態',
      enumTypeDescription: [
        `${UserStatusEnum.UNVERIFIED}：停用`,
        `${UserStatusEnum.VERIFIED}：啟用`,
        `${UserStatusEnum.SUSPENDED}：邀請中`,
      ],
    }),
    enum: UserStatusEnum,
    required: false,
  })
  @IsOptional()
  @IsEnum(UserStatusEnum)
  readonly status?: UserStatusEnum;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '關鍵字',
      remarks: ['模糊搜尋名字（姓氏+名字）', '模糊搜尋 email'],
    }),
    required: false,
  })
  @IsOptional()
  @IsString()
  readonly keyword?: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '每頁筆數',
      remarks: ['需大於等於 1'],
      defaultValue: '30',
    }),
    required: false,
    example: 30,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @TransformToNumber()
  readonly size: number = 30;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '頁數',
      remarks: ['需大於等於 1'],
      defaultValue: '1',
    }),
    required: false,
    example: 1,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @TransformToNumber()
  readonly page: number = 1;
}
