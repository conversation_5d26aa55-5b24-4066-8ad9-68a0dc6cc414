import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsInt,
  ArrayUnique,
  IsOptional,
  ArrayMinSize,
  IsEnum,
  IsEmail,
} from 'class-validator';
import { customApiPropertyDescription } from '../../../utils/swagger';
import { UpdateUserStatusEnum } from '../users.interface';
import { IsNotBlank } from '../../../decorators/validations/IsNotBlank';

export class UpdateUserDto {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '部門',
    }),
    required: false,
    example: '產品研發部',
  })
  @IsOptional()
  @IsString()
  @IsNotBlank()
  readonly department?: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '職稱',
    }),
    required: false,
    example: 'RD',
  })
  @IsOptional()
  @IsString()
  @IsNotBlank()
  readonly title?: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '姓氏',
    }),
    required: false,
    example: '姓氏',
  })
  @IsOptional()
  @IsString()
  @IsNotBlank()
  readonly lastName?: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '名字',
    }),
    required: false,
    example: '名字',
  })
  @IsOptional()
  @IsString()
  @IsNotBlank()
  readonly firstName?: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '信箱',
    }),
    required: false,
    example: null,
  })
  @IsOptional()
  @IsString()
  @IsEmail()
  readonly email?: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '所綁定的角色 IDs',
      limitations: ['不可傳入空陣列', '不可重複'],
      remarks: ['對應前端「角色」欄位'],
    }),
    type: [Number],
    required: false,
    example: [1, 2],
  })
  @IsOptional()
  @IsInt({ each: true })
  @ArrayMinSize(1)
  @ArrayUnique()
  readonly roleIds?: number[];

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '更新狀態',
      enumTypeDescription: [
        `${UpdateUserStatusEnum.ENABLE}：啟用`,
        `${UpdateUserStatusEnum.DISABLE}：停用`,
      ],
    }),
    enum: UpdateUserStatusEnum,
    required: false,
  })
  @IsOptional()
  @IsEnum(UpdateUserStatusEnum)
  readonly status?: UpdateUserStatusEnum;
}
