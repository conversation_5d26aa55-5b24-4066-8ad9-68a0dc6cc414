import { Injectable } from '@nestjs/common';
import {
  CreateRoleResponseFilterPayload,
  RolesFilter,
  GetRoleListResponseFilterPayload,
  RolesResponseFilterOriginalResult,
  GetRoleDetailResponseFilterPayload,
  RolesResponseFilterResult,
  RolesResponseFilterTypeEnum,
  GetSelfRoleListResponseFilterPayload,
} from './roles.filter.interface';
import { CreateRoleResult } from '../entities/create-role.entity';
import { GetRoleListResult } from '../entities/get-role-list.entity';
import { GetRoleDetailResult } from '../entities/get-role-detail.entity';
import { GetSelfRoleListResult } from '../entities/get-self-role-list.entity';

@Injectable()
export class RolesResponseFilter
  implements
    RolesFilter<RolesResponseFilterOriginalResult, RolesResponseFilterResult>
{
  getResponse(
    orig: RolesResponseFilterOriginalResult,
  ): RolesResponseFilterResult {
    switch (orig.type) {
      case RolesResponseFilterTypeEnum.GET_ROLE_LIST: {
        const payload = orig.payload as GetRoleListResponseFilterPayload;
        let result = new GetRoleListResult();
        result = {
          roles: payload.roles,
          totalCount: payload.totalCount,
          totalPage: Math.ceil(payload.totalCount / payload.size),
          currentPage: payload.page,
        };
        return result;
      }

      case RolesResponseFilterTypeEnum.CREATE_ROLE: {
        const payload = orig.payload as CreateRoleResponseFilterPayload;
        const result = new CreateRoleResult();
        result.id = payload.id;
        return result;
      }

      case RolesResponseFilterTypeEnum.GET_ROLE_DETAIL: {
        const payload = orig.payload as GetRoleDetailResponseFilterPayload;
        let result = new GetRoleDetailResult();
        result = { ...payload };
        return result;
      }

      case RolesResponseFilterTypeEnum.GET_SELF_ROLE_LIST: {
        const payload = orig.payload as GetSelfRoleListResponseFilterPayload;
        let result = new GetSelfRoleListResult();
        result = { ...payload };
        return result;
      }
    }
  }
}
