import { CreateRoleResult } from '../entities/create-role.entity';
import { GetRoleListResult } from '../entities/get-role-list.entity';
import { GetRoleDetailResult } from '../entities/get-role-detail.entity';
import { GetSelfRoleListResult } from '../entities/get-self-role-list.entity';
import { CreateRoleOperationLogRes } from '../roles.interface';

export enum RolesResponseFilterTypeEnum {
  GET_ROLE_LIST = 'GET_ROLE_LIST',
  CREATE_ROLE = 'CREATE_ROLE',
  GET_ROLE_DETAIL = 'GET_ROLE_DETAIL',
  GET_SELF_ROLE_LIST = 'GET_SELF_ROLE_LIST',
}

export interface RolesFilter<T, U> {
  getResponse(orig: T, operationLogResult?: CreateRoleOperationLogRes): U;
}

type RoleCreatorOrUpdater = {
  firstName: string;
  lastName: string;
  email: string;
};

export interface GetRoleListResponseFilterPayload {
  roles: {
    id: number;
    name: string;
    description: string;
    createdAt: number;
    creator: RoleCreatorOrUpdater;
    updatedAt: number;
    updater: RoleCreatorOrUpdater;
  }[];
  totalCount: number;
  page: number;
  size: number;
}

export interface CreateRoleResponseFilterPayload {
  id: number;
}

export interface GetRoleDetailResponseFilterPayload {
  id: number;
  name: string;
  description: string;
  permissionActionKeys: string[];
}

export interface GetSelfRoleListResponseFilterPayload {
  roles: {
    id: number;
    name: string;
    description: string;
    permissionActionKeys: string[];
  }[];
}

export interface RolesResponseFilterOriginalResult {
  type: RolesResponseFilterTypeEnum;
  payload:
    | GetRoleListResponseFilterPayload
    | CreateRoleResponseFilterPayload
    | GetRoleDetailResponseFilterPayload
    | GetSelfRoleListResponseFilterPayload;
}

export type RolesResponseFilterResult =
  | GetRoleListResult
  | CreateRoleResult
  | GetRoleDetailResult
  | GetSelfRoleListResult;
