import { HttpException, HttpStatus } from '@nestjs/common';
import { adminRoleInfo } from '../../../commands/seeds/admin-role.seed';
import { RolesFilter } from './roles.filter.interface';
import { CreateRoleOperationLogRes } from '../roles.interface';

export class RolesExceptionFilter implements RolesFilter<Error, HttpException> {
  getResponse(
    { message: originalErrorMessage }: Error,
    operationLogResult?: CreateRoleOperationLogRes,
  ): HttpException {
    let errorMessage: string;
    let statusCode: number;

    switch (originalErrorMessage) {
      case 'Role not found.':
        errorMessage = '查無角色';
        statusCode = HttpStatus.NOT_FOUND;
        break;

      case 'The system admin role cannot be edited.':
        errorMessage = `無法編輯「${adminRoleInfo.name}」`;
        statusCode = HttpStatus.BAD_REQUEST;
        break;

      case "Can't edit the role that you bind.":
        errorMessage = '無法編輯您所綁定的角色';
        statusCode = HttpStatus.BAD_REQUEST;
        break;

      case "Can't delete the role that you bind.":
        errorMessage = '無法刪除您所綁定的角色';
        statusCode = HttpStatus.BAD_REQUEST;
        break;

      case 'The system admin role cannot be deleted.':
        errorMessage = `無法刪除「${adminRoleInfo.name}」`;
        statusCode = HttpStatus.BAD_REQUEST;
        break;

      case `Can't delete role that still has users.`:
        errorMessage = '目前尚有使用者綁定該角色，請先移除後再刪除角色權限';
        statusCode = HttpStatus.BAD_REQUEST;
        break;

      default:
        errorMessage = originalErrorMessage;
        statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
    }

    return new HttpException(
      {
        message: errorMessage,
        operationLogResult,
      },
      statusCode,
    );
  }
}
