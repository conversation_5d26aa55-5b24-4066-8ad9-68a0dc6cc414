import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, IsString, Min } from 'class-validator';
import { TransformToNumber } from '../../../decorators/transformers/TransformToNumber';
import { customApiPropertyDescription } from '../../../utils/swagger';

export class GetRoleListDto {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '關鍵字',
      remarks: ['模糊搜尋名稱'],
    }),
    required: false,
    example: '管理員',
  })
  @IsOptional()
  @IsString()
  readonly keyword?: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '每頁筆數',
      remarks: ['若有帶入，需大於等於 1'],
    }),
    required: false,
    example: 30,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @TransformToNumber()
  readonly size?: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '當前頁數',
      remarks: ['若有帶入，需大於等於 1'],
    }),
    required: false,
    example: 1,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @TransformToNumber()
  readonly page?: number;
}
