import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  ArrayUnique,
  IsOptional,
  ArrayMinSize,
} from 'class-validator';
import { customApiPropertyDescription } from '../../../utils/swagger';
import { IsNotBlank } from '../../../decorators/validations/IsNotBlank';

export class UpdateRoleDto {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '名稱',
    }),
    example: '管理員',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsNotBlank()
  readonly name?: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '說明',
    }),
    example: '可以使用權限管理',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsNotBlank()
  readonly description?: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '權限 Keys',
      limitations: ['至少一個', '不可重複'],
    }),
    type: [String],
    required: false,
    example: ['AUTHORIZATION_MANAGEMENT_ROLE_MANAGEMENT_CREATE'],
  })
  @IsOptional()
  @IsString({ each: true })
  @ArrayUnique()
  @ArrayMinSize(1)
  readonly permissionActionKeys?: string[];
}
