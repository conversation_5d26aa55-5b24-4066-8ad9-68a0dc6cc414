import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { isEmpty, pick } from 'lodash';
import { Role } from '../../database/role.entity';
import { In, ILike, ObjectLiteral, Repository } from 'typeorm';
import { Enforcer } from 'casbin';
import { CasbinService } from '../casbin/casbin.service';
import { PermissionConfig } from '../permission-object-groups/permission-object-groups.interface';
import { PermissionAction } from '../../database/permission_action.entity';
import {
  CreateFailedOperationLogReq,
  CreateSuccessfulOperationLogReq,
} from '../operation-logs/operation-logs.interface';
import { OperationLogsService } from '../operation-logs/operation-logs.service';
import {
  AddPoliciesToRoleReq,
  CreateRoleReq,
  GetRoleListReq,
  CreateRoleOperationLogReq,
  RoleOperationTypeEnum,
  GetRoleDetailRes,
  GetRoleListRes,
  UpdateRoleReq,
  CreateRoleRes,
  DeleteRoleReq,
  GetSelfRoleListRes,
  GetRoleListByUserIdRes,
  CreateRoleOperationLogRes,
} from './roles.interface';
import {
  bigintToNumber,
  getPagingSkip,
  getPagingTake,
} from '../../utils/value-conversion';
import { adminRoleId } from '../../commands/seeds/admin-role.seed';
import { OperationConfig } from '../operation-object-groups/operation-object-groups.interface';

@Injectable()
export class RolesService {
  constructor(
    private readonly casbinService: CasbinService,
    private readonly operationLogsService: OperationLogsService,
    @InjectRepository(Role)
    private readonly roleRepository: Repository<Role>,
    @InjectRepository(PermissionAction)
    private readonly permissionActionRepository: Repository<PermissionAction>,
    @Inject('CASBIN_ENFORCER')
    private enforcer: Enforcer,
  ) {}

  public async createRoleOperationLog({
    type,
    isSuccess,
    operatorUserId,
    roleId,
    roleName,
    roleOriginalData,
    inputData,
    outputData,
    error,
  }: CreateRoleOperationLogReq): Promise<CreateRoleOperationLogRes> {
    const successfulOrFailedOperationLogs:
      | CreateSuccessfulOperationLogReq[]
      | CreateFailedOperationLogReq[] = [];
    const failedOperationLogs: CreateFailedOperationLogReq[] = [];

    const targetId = roleId?.toString();
    const targetName = roleName;
    const targetOriginalData = roleOriginalData;
    const creatorUserId = operatorUserId;

    switch (type) {
      case RoleOperationTypeEnum.GET_ROLE_LIST: {
        const {
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
        } =
          OperationConfig.AUTHORIZATION_MANAGEMENT.ROLE_MANAGEMENT
            .GET_ROLE_LIST;
        successfulOrFailedOperationLogs.push({
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
          inputData,
          outputData,
          creatorUserId,
        });
        break;
      }
      case RoleOperationTypeEnum.CREATE_ROLE: {
        const {
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
        } =
          OperationConfig.AUTHORIZATION_MANAGEMENT.ROLE_MANAGEMENT.CREATE_ROLE;
        successfulOrFailedOperationLogs.push({
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
          targetId,
          targetName,
          inputData,
          outputData,
          creatorUserId,
        });
        break;
      }
      case RoleOperationTypeEnum.GET_ROLE_DETAIL: {
        const {
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
        } =
          OperationConfig.AUTHORIZATION_MANAGEMENT.ROLE_MANAGEMENT
            .GET_ROLE_DETAIL;
        successfulOrFailedOperationLogs.push({
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
          targetId,
          targetName,
          inputData,
          outputData,
          creatorUserId,
        });
        break;
      }
      case RoleOperationTypeEnum.UPDATE_ROLE: {
        const {
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
        } =
          OperationConfig.AUTHORIZATION_MANAGEMENT.ROLE_MANAGEMENT.UPDATE_ROLE;
        successfulOrFailedOperationLogs.push({
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
          targetId,
          targetName,
          targetOriginalData,
          inputData,
          creatorUserId,
        });
        break;
      }
      case RoleOperationTypeEnum.DELETE_ROLE: {
        const {
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
        } =
          OperationConfig.AUTHORIZATION_MANAGEMENT.ROLE_MANAGEMENT.DELETE_ROLE;
        successfulOrFailedOperationLogs.push({
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
          targetId,
          targetName,
          inputData,
          creatorUserId,
        });
        break;
      }
      default:
        break;
    }
    if (isSuccess) {
      return this.operationLogsService.createSuccessfulOperationLogs(
        successfulOrFailedOperationLogs,
      );
    }

    successfulOrFailedOperationLogs.forEach(
      (successfulOrFailedOperationLog) => {
        failedOperationLogs.push({
          ...successfulOrFailedOperationLog,
          failCode: error?.code || null,
          failReason: error?.message || error,
        });
      },
    );
    return this.operationLogsService.createFailedOperationLogs(
      failedOperationLogs,
    );
  }

  /**
   * @description 檢查是否有檢視的權限
   */
  public async checkAuthRoleReadPermission(
    operatorUserId: number,
  ): Promise<void> {
    await this.casbinService.checkPermission(
      operatorUserId,
      PermissionConfig.AUTHORIZATION_MANAGEMENT.ROLE_MANAGEMENT.READ,
    );
  }

  /**
   * @description 取得角色列表（可使用角色名稱模糊搜尋、可分頁）
   */
  public async getRoleList({
    keyword,
    size,
    page,
  }: GetRoleListReq): Promise<GetRoleListRes> {
    const whereConditions: ObjectLiteral = {};

    // MEMO: 角色名稱模糊搜尋
    if (keyword) {
      whereConditions.name = ILike(`%${keyword}%`);
    }

    const queryBuilder = this.roleRepository
      .createQueryBuilder('role')
      .select([
        'role.id',
        'role.name',
        'role.description',
        'role.createdAt',
        'role.updatedAt',
      ])
      .where('role.deleted_at IS NULL')
      .leftJoin('role.creator', 'creator')
      .addSelect(['creator.firstName', 'creator.lastName', 'creator.email'])
      .leftJoin('role.updater', 'updater')
      .addSelect(['updater.firstName', 'updater.lastName', 'updater.email'])
      .orderBy('role.createdAt', 'DESC');

    if (!isEmpty(whereConditions)) {
      queryBuilder.andWhere(whereConditions);
    }

    if (size && page) {
      queryBuilder.skip(getPagingSkip(size, page)).take(getPagingTake(size));
    }

    const [roles, totalCount] = await queryBuilder.getManyAndCount();

    return {
      roles: roles.map((role) => ({
        id: role.id,
        name: role.name,
        description: role.description,
        creator: role.creator
          ? {
              firstName: role.creator.firstName,
              lastName: role.creator.lastName,
              email: role.creator.email,
            }
          : null,
        createdAt: bigintToNumber(role.createdAt),
        updater: role.updater
          ? {
              firstName: role.updater.firstName,
              lastName: role.updater.lastName,
              email: role.updater.email,
            }
          : null,
        updatedAt: bigintToNumber(role.updatedAt),
      })),
      totalCount,
    };
  }

  /**
   * @description 檢查是否有新增的權限
   */
  public async checkAuthRoleCreatePermission(
    operatorUserId: number,
  ): Promise<void> {
    await this.casbinService.checkPermission(
      operatorUserId,
      PermissionConfig.AUTHORIZATION_MANAGEMENT.ROLE_MANAGEMENT.CREATE,
    );
  }

  /**
   * @description 檢查權限 Keys
   * @access private
   */
  private async checkPermissionActionKeys(
    permissionActionKeys: string[],
  ): Promise<boolean> {
    const count = await this.permissionActionRepository.count({
      where: {
        key: In(permissionActionKeys),
      },
    });
    return count === permissionActionKeys.length;
  }

  /**
   * @description 根據 action key 取得 object key
   */
  private async getPolicyListByPermissionActionKeys(
    permissionActionKeys: string[],
  ): Promise<Map<string, Set<string>>> {
    const actions = await this.permissionActionRepository
      .createQueryBuilder('action')
      .select()
      .innerJoin('action.permissionObject', 'object')
      .addSelect(['object.key'])
      .where(`action.key IN (:...key)`, { key: permissionActionKeys })
      .getMany();

    const policies: Map<string, Set<string>> = new Map();
    actions.forEach((action) => {
      if (policies.has(action.permissionObject.key)) {
        policies.get(action.permissionObject.key).add(action.key);
      } else {
        policies.set(action.permissionObject.key, new Set([action.key]));
      }
    });
    return policies;
  }

  /**
   * @description 角色綁定權限
   * @access private
   */
  private async addPoliciesToRole({
    roleId,
    permissionActionKeys,
  }: AddPoliciesToRoleReq): Promise<void> {
    if (!permissionActionKeys.length) {
      throw new Error(
        'The permissionActionKeys must contains at least one key.',
      );
    }

    const policies =
      await this.getPolicyListByPermissionActionKeys(permissionActionKeys);

    const rules = [];
    policies.forEach((permissionActionKeys, permissionObjectKey) => {
      permissionActionKeys.forEach((permissionActionKey) =>
        rules.push([
          `role::${roleId}`,
          permissionObjectKey,
          permissionActionKey,
        ]),
      );
    });
    await this.enforcer.addPolicies(rules);
  }

  /**
   * @description 新增角色，並且綁定權限
   */
  public async createRole({
    operatorUserId,
    createInfo,
  }: CreateRoleReq): Promise<CreateRoleRes> {
    const checkPermissionActionKeysRes = await this.checkPermissionActionKeys(
      createInfo.permissionActionKeys,
    );
    if (!checkPermissionActionKeysRes) {
      throw new Error(
        "The permissionActionKeys contains key that doesn't exist",
      );
    }

    const role = await this.roleRepository.save({
      name: createInfo.name,
      description: createInfo.description,
      creatorUserId: operatorUserId,
      updaterUserId: operatorUserId,
    });

    await this.addPoliciesToRole({
      roleId: role.id,
      permissionActionKeys: createInfo.permissionActionKeys,
    });

    return {
      id: role.id,
    };
  }

  /**
   * @description 取得使用者綁定的角色 IDs
   */
  public async getRoleListByUserId(
    operatorUserId: number,
  ): Promise<GetRoleListByUserIdRes> {
    const roleIds = await this.enforcer
      .getRolesForUser(`user::${operatorUserId}`)
      .then((roleIdStrs) => {
        return roleIdStrs.map((str) => parseInt(str.split('::')[1], 10));
      });
    const roles = await this.roleRepository.find({
      where: {
        id: In(roleIds),
      },
      select: ['id', 'name'],
    });
    return {
      roleIds,
      roles,
    };
  }

  /**
   * @description 確認所有角色都存在
   */
  public async checkRoleIds(roleIds: number[]): Promise<boolean> {
    const count = await this.roleRepository.count({
      where: {
        id: In(roleIds),
      },
    });
    return count === roleIds.length;
  }

  /**
   * @description 取得角色詳細資訊（不存在時會回傳錯誤）
   */
  public async getRoleDetail(roleId: number): Promise<GetRoleDetailRes> {
    const role = await this.roleRepository.findOneBy({ id: roleId });
    if (!role || role.deleterUserId) {
      throw new Error('Role not found.');
    }

    const permissions = await this.enforcer.getImplicitPermissionsForUser(
      `role::${roleId}`,
    );

    return {
      id: role.id,
      name: role.name,
      description: role.description,
      permissionActionKeys:
        permissions.map(([, , permissionActionKey]) => permissionActionKey) ||
        [],
    };
  }

  /**
   * @description 檢查是否有編輯的權限
   */
  public async checkAuthRoleUpdatePermission(
    operatorUserId: number,
  ): Promise<void> {
    await this.casbinService.checkPermission(
      operatorUserId,
      PermissionConfig.AUTHORIZATION_MANAGEMENT.ROLE_MANAGEMENT.UPDATE,
    );
  }

  /**
   * @description 編輯角色
   */
  public async updateRole({
    roleId,
    operatorUserId,
    updateInfo,
  }: UpdateRoleReq): Promise<void> {
    if (roleId === adminRoleId) {
      throw new Error('The system admin role cannot be edited.');
    }

    // MEMO: 無法編輯自身所綁定的角色
    const { roles } = await this.getSelfRoleList(operatorUserId);
    if (roles.some(({ id }) => id === roleId)) {
      throw new Error("Can't edit the role that you bind.");
    }

    const { permissionActionKeys } = updateInfo;
    if (permissionActionKeys.length) {
      const checkPermissionActionKeysRes =
        await this.checkPermissionActionKeys(permissionActionKeys);
      if (!checkPermissionActionKeysRes) {
        throw new Error(
          "The permissionActionKeys contains key that doesn't exist",
        );
      }
      await this.enforcer.deletePermissionsForUser(`role::${roleId}`);
      await this.addPoliciesToRole({
        roleId,
        permissionActionKeys: permissionActionKeys,
      });
    }

    const pickUpdateInfo: Partial<Role> = pick(updateInfo, [
      'name',
      'description',
    ]);
    await this.roleRepository.update(roleId, {
      ...pickUpdateInfo,
      updaterUserId: operatorUserId,
      updatedAt: BigInt(new Date().getTime()),
    });
  }

  /**
   * @description 檢查是否有刪除的權限
   */
  public async checkAuthRoleDeletePermission(
    operatorUserId: number,
  ): Promise<void> {
    await this.casbinService.checkPermission(
      operatorUserId,
      PermissionConfig.AUTHORIZATION_MANAGEMENT.ROLE_MANAGEMENT.DELETE,
    );
  }

  /**
   * @description 刪除角色與角色所綁定的權限
   */
  public async deleteRole({
    roleId,
    operatorUserId,
  }: DeleteRoleReq): Promise<void> {
    if (roleId === adminRoleId) {
      throw new Error('The system admin role cannot be deleted.');
    }

    const { roles } = await this.getSelfRoleList(operatorUserId);
    if (roles.some(({ id }) => id === roleId)) {
      throw new Error("Can't delete the role that you bind.");
    }

    const usersForRole = await this.enforcer.getUsersForRole(`role::${roleId}`);
    if (usersForRole.length) {
      throw new Error("Can't delete role that still has users.");
    }

    await this.enforcer.deleteRole(`role::${roleId}`);
    await this.roleRepository.update(roleId, {
      deletedAt: BigInt(new Date().getTime()),
      deleterUserId: operatorUserId,
    });
  }

  /**
   * @description 取得自己所綁定的角色列表
   */
  public async getSelfRoleList(userId: number): Promise<GetSelfRoleListRes> {
    const roleIds = await this.enforcer
      .getRolesForUser(`user::${userId}`)
      .then((ids) => ids.map((id) => parseInt(id.split('::')[1], 10)));

    return {
      roles: await Promise.all(
        roleIds.map(async (roleId) => {
          const role = await this.getRoleDetail(roleId);
          return {
            id: role.id,
            name: role.name,
            description: role.description,
            permissionActionKeys: role.permissionActionKeys,
          };
        }),
      ),
    };
  }
}
