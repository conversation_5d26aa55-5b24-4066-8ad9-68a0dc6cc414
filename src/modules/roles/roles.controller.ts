import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  HttpCode,
  Req,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOkResponse,
  ApiCreatedResponse,
  ApiOperation,
} from '@nestjs/swagger';
import { RolesService } from './roles.service';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import UserAuthGuard from '../auth/guards/user-auth.guard';
import { ApiResultAndCreateOperationLogResult } from '../../interceptors/response/response.interface';
import { CreateRoleResult, CreateRoleRo } from './entities/create-role.entity';
import { RolesResponseFilter } from './filters/roles-response.filter';
import { RolesExceptionFilter } from './filters/roles-exception.filter';
import { RolesResponseFilterTypeEnum } from './filters/roles.filter.interface';
import {
  GetRoleListResult,
  GetRoleListRo,
} from './entities/get-role-list.entity';
import { GetRoleListDto } from './dto/get-role-list.dto';
import {
  GetRoleDetailResult,
  GetRoleDetailRo,
} from './entities/get-role-detail.entity';
import { UpdateRoleRo } from './entities/update-role.entity';
import { DeleteRoleRo } from './entities/delete-role.entity';
import {
  CreateRoleOperationLogReq,
  RoleOperationTypeEnum,
} from './roles.interface';
import { customApiOperationDescription } from '../../utils/swagger';
import {
  GetSelfRoleListResult,
  GetSelfRoleListRo,
} from './entities/get-self-role-list.entity';

@ApiBearerAuth()
@UseGuards(UserAuthGuard)
@Controller('roles')
export class RolesController {
  constructor(
    private readonly rolesService: RolesService,
    private readonly rolesResponseFilter: RolesResponseFilter,
    private readonly rolesExceptionFilter: RolesExceptionFilter,
  ) {}

  @Get()
  @ApiOperation({
    summary: '取得角色列表',
    description: customApiOperationDescription({
      permissions: ['權限管理 > 角色設定 > 檢視'],
      operationLogs: ['權限管理 > 角色設定 > 查詢角色清單'],
    }),
  })
  @ApiOkResponse({
    type: GetRoleListRo,
  })
  async getRoleList(
    @Req() req,
    @Query() getRoleListDto: GetRoleListDto,
  ): Promise<ApiResultAndCreateOperationLogResult<GetRoleListResult>> {
    const { userId: operatorUserId } = req.user;
    const createRoleOperationLogReq: CreateRoleOperationLogReq = {
      isSuccess: true,
      type: RoleOperationTypeEnum.GET_ROLE_LIST,
      operatorUserId,
      inputData: getRoleListDto,
    };
    try {
      await this.rolesService.checkAuthRoleReadPermission(operatorUserId);
      const res = await this.rolesService.getRoleList(getRoleListDto);
      const result = this.rolesResponseFilter.getResponse({
        type: RolesResponseFilterTypeEnum.GET_ROLE_LIST,
        payload: {
          ...res,
          page: getRoleListDto.page,
          size: getRoleListDto.size,
        },
      }) as GetRoleListResult;
      const operationLogResult = await this.rolesService.createRoleOperationLog(
        {
          ...createRoleOperationLogReq,
          outputData: result,
        },
      );
      return {
        result,
        operationLogResult,
      };
    } catch (error) {
      const operationLogResult = await this.rolesService.createRoleOperationLog(
        {
          ...createRoleOperationLogReq,
          isSuccess: false,
          error,
        },
      );
      throw this.rolesExceptionFilter.getResponse(error, operationLogResult);
    }
  }

  @Post()
  @HttpCode(201)
  @ApiOperation({
    summary: '新增角色',
    description: customApiOperationDescription({
      permissions: ['權限管理 > 角色設定 > 新增'],
      operationLogs: ['權限管理 > 角色設定 > 新增角色'],
    }),
  })
  @ApiBody({
    type: CreateRoleDto,
  })
  @ApiCreatedResponse({
    type: CreateRoleRo,
  })
  async createRole(
    @Req() req,
    @Body() createRoleDto: CreateRoleDto,
  ): Promise<ApiResultAndCreateOperationLogResult<CreateRoleResult>> {
    const { userId: operatorUserId } = req.user;
    const createRoleOperationLogReq: CreateRoleOperationLogReq = {
      isSuccess: true,
      type: RoleOperationTypeEnum.CREATE_ROLE,
      operatorUserId,
      roleName: createRoleDto.name,
      inputData: createRoleDto,
    };
    try {
      await this.rolesService.checkAuthRoleCreatePermission(operatorUserId);
      const { id: createdRoleId } = await this.rolesService.createRole({
        operatorUserId,
        createInfo: createRoleDto,
      });
      createRoleOperationLogReq.roleId = createdRoleId;
      const result = this.rolesResponseFilter.getResponse({
        type: RolesResponseFilterTypeEnum.CREATE_ROLE,
        payload: {
          id: createdRoleId,
        },
      }) as CreateRoleResult;
      const operationLogResult = await this.rolesService.createRoleOperationLog(
        {
          ...createRoleOperationLogReq,
          outputData: result,
        },
      );
      return {
        result,
        operationLogResult,
      };
    } catch (error) {
      const operationLogResult = await this.rolesService.createRoleOperationLog(
        {
          ...createRoleOperationLogReq,
          isSuccess: false,
          error,
        },
      );
      throw this.rolesExceptionFilter.getResponse(error, operationLogResult);
    }
  }

  @Get(':roleId')
  @ApiOperation({
    summary: '取得角色詳細資訊',
    description: customApiOperationDescription({
      permissions: ['權限管理 > 角色設定 > 檢視'],
      operationLogs: ['權限管理 > 角色設定 > 查詢角色'],
    }),
  })
  @ApiOkResponse({
    type: GetRoleDetailRo,
  })
  async getRoleDetail(
    @Req() req,
    @Param('roleId', ParseIntPipe) roleId: number,
  ): Promise<ApiResultAndCreateOperationLogResult<GetRoleDetailResult>> {
    const { userId: operatorUserId } = req.user;
    const createRoleOperationLogReq: CreateRoleOperationLogReq = {
      isSuccess: true,
      type: RoleOperationTypeEnum.GET_ROLE_DETAIL,
      operatorUserId,
      roleId,
    };
    try {
      await this.rolesService.checkAuthRoleReadPermission(operatorUserId);
      const res = await this.rolesService.getRoleDetail(roleId);
      createRoleOperationLogReq.roleName = res.name;
      const result = this.rolesResponseFilter.getResponse({
        type: RolesResponseFilterTypeEnum.GET_ROLE_DETAIL,
        payload: res,
      }) as GetRoleDetailResult;
      const operationLogResult = await this.rolesService.createRoleOperationLog(
        {
          ...createRoleOperationLogReq,
          outputData: result,
        },
      );
      return {
        result,
        operationLogResult,
      };
    } catch (error) {
      const operationLogResult = await this.rolesService.createRoleOperationLog(
        {
          ...createRoleOperationLogReq,
          isSuccess: false,
          error,
        },
      );
      throw this.rolesExceptionFilter.getResponse(error, operationLogResult);
    }
  }

  @Patch(':roleId')
  @ApiOperation({
    summary: '編輯角色',
    description: customApiOperationDescription({
      permissions: ['權限管理 > 角色設定 > 編輯'],
      operationLogs: ['權限管理 > 角色設定 > 編輯角色'],
    }),
  })
  @ApiBody({
    type: UpdateRoleDto,
  })
  @ApiOkResponse({
    type: UpdateRoleRo,
  })
  async updateRole(
    @Req() req,
    @Param('roleId', ParseIntPipe) roleId: number,
    @Body() updateRoleDto: UpdateRoleDto,
  ): Promise<ApiResultAndCreateOperationLogResult<void>> {
    const { userId: operatorUserId } = req.user;
    const createRoleOperationLogReq: CreateRoleOperationLogReq = {
      isSuccess: true,
      type: RoleOperationTypeEnum.UPDATE_ROLE,
      operatorUserId,
      roleId,
      inputData: updateRoleDto,
    };
    try {
      await this.rolesService.checkAuthRoleUpdatePermission(operatorUserId);
      const roleInfo = await this.rolesService.getRoleDetail(roleId);
      createRoleOperationLogReq.roleName = roleInfo.name;
      createRoleOperationLogReq.roleOriginalData = roleInfo;
      await this.rolesService.updateRole({
        roleId,
        operatorUserId,
        updateInfo: updateRoleDto,
      });
      const operationLogResult = await this.rolesService.createRoleOperationLog(
        createRoleOperationLogReq,
      );
      return {
        operationLogResult,
      };
    } catch (error) {
      const operationLogResult = await this.rolesService.createRoleOperationLog(
        {
          ...createRoleOperationLogReq,
          isSuccess: false,
          error,
        },
      );
      throw this.rolesExceptionFilter.getResponse(error, operationLogResult);
    }
  }

  @Delete(':roleId')
  @ApiOperation({
    summary: '刪除角色',
    description: customApiOperationDescription({
      permissions: ['權限管理 > 角色設定 > 刪除'],
      operationLogs: ['權限管理 > 角色設定 > 刪除角色'],
    }),
  })
  @ApiOkResponse({
    type: DeleteRoleRo,
  })
  async deleteRole(
    @Req() req,
    @Param('roleId', ParseIntPipe) roleId: number,
  ): Promise<ApiResultAndCreateOperationLogResult<void>> {
    const { userId: operatorUserId } = req.user;
    const createRoleOperationLogReq: CreateRoleOperationLogReq = {
      isSuccess: true,
      type: RoleOperationTypeEnum.DELETE_ROLE,
      operatorUserId,
      roleId,
    };
    try {
      await this.rolesService.checkAuthRoleDeletePermission(operatorUserId);
      const { name: roleName } = await this.rolesService.getRoleDetail(roleId);
      createRoleOperationLogReq.roleName = roleName;
      await this.rolesService.deleteRole({ roleId, operatorUserId });
      const operationLogResult = await this.rolesService.createRoleOperationLog(
        createRoleOperationLogReq,
      );
      return {
        operationLogResult,
      };
    } catch (error) {
      const operationLogResult = await this.rolesService.createRoleOperationLog(
        {
          ...createRoleOperationLogReq,
          isSuccess: false,
          error,
        },
      );
      throw this.rolesExceptionFilter.getResponse(error, operationLogResult);
    }
  }

  @Get('self')
  @ApiOperation({
    summary: '取得自身帳號綁定的角色與權限',
  })
  @ApiOkResponse({
    type: GetSelfRoleListRo,
  })
  async getSelfRoleList(@Req() req): Promise<GetSelfRoleListResult> {
    const { userId } = req.user;
    try {
      const res = await this.rolesService.getSelfRoleList(userId);
      const result = this.rolesResponseFilter.getResponse({
        type: RolesResponseFilterTypeEnum.GET_SELF_ROLE_LIST,
        payload: res,
      }) as GetSelfRoleListResult;
      return result;
    } catch (error) {
      throw this.rolesExceptionFilter.getResponse(error);
    }
  }
}
