import { <PERSON>du<PERSON> } from '@nestjs/common';
import { RolesService } from './roles.service';
import { RolesController } from './roles.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Role } from '../../database/role.entity';
import { CasbinModule } from '../casbin/casbin.module';
import { RolesResponseFilter } from './filters/roles-response.filter';
import { RolesExceptionFilter } from './filters/roles-exception.filter';
import { OperationLogsModule } from '../operation-logs/operation-logs.module';
import { PermissionAction } from '../../database/permission_action.entity';
import { PermissionObject } from '../../database/permission_object.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Role, PermissionAction, PermissionObject]),
    CasbinModule,
    OperationLogsModule,
  ],
  controllers: [RolesController],
  providers: [RolesService, RolesResponseFilter, RolesExceptionFilter],
  exports: [RolesService],
})
export class RolesModule {}
