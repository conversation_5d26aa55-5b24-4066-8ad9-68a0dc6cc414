import { ApiProperty } from '@nestjs/swagger';
import {
  ApiResponseStatusEnum,
  LogResponseStatusEnum,
  ResponseInterface,
} from '../../../interceptors/response/response.interface';
import { customApiPropertyDescription } from '../../../utils/swagger';

class RoleCreatorOrUpdater {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '名字',
    }),
    example: '名字',
  })
  firstName: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '姓氏',
    }),
    example: '姓氏',
  })
  lastName: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '信箱',
    }),
    example: '<EMAIL>',
  })
  email: string;
}

class Role {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'ID',
    }),
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '名稱',
    }),
    example: '管理員',
  })
  name: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '說明',
    }),
    example: '可以使用權限設定所有功能',
  })
  description: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '建立人',
    }),
  })
  creator: RoleCreatorOrUpdater;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '建立時間',
    }),
    example: 1622505600000,
  })
  createdAt: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '最後更新人',
    }),
  })
  updater: RoleCreatorOrUpdater;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '最後更新時間',
    }),
    example: 1622505600000,
  })
  updatedAt: number;
}

export class GetRoleListResult {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '角色列表',
    }),
    type: [Role],
  })
  roles: Role[];

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '總數量',
    }),
    example: 1,
  })
  totalCount: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '總頁數',
    }),
    example: 1,
  })
  totalPage: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '當前頁數',
    }),
    example: 1,
  })
  currentPage: number;
}

export class GetRoleListRo implements ResponseInterface<GetRoleListResult> {
  @ApiProperty({ example: 200 })
  code: number;

  @ApiProperty({
    enum: LogResponseStatusEnum,
    example: LogResponseStatusEnum.SUCCESS,
  })
  logStatus: LogResponseStatusEnum;

  @ApiProperty({
    enum: ApiResponseStatusEnum,
    example: ApiResponseStatusEnum.SUCCESS,
  })
  status: ApiResponseStatusEnum;

  @ApiProperty({ type: GetRoleListResult })
  data: GetRoleListResult;
}
