import { ApiProperty } from '@nestjs/swagger';
import {
  ApiResponseStatusEnum,
  ResponseInterface,
} from '../../../interceptors/response/response.interface';
import { customApiPropertyDescription } from '../../../utils/swagger';

class SelfRole {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'ID',
    }),
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '名稱',
    }),
    example: '管理員',
  })
  name: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '說明',
    }),
    example: '可以使用權限設定所有功能',
  })
  description: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '所綁定的權限 Keys',
    }),
    type: [String],
    example: ['AUTHORIZATION_MANAGEMENT_ROLE_MANAGEMENT_CREATE'],
  })
  permissionActionKeys: string[];
}

export class GetSelfRoleListResult {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '所綁定的角色列表',
    }),
    type: [SelfRole],
  })
  roles: SelfRole[];
}

export class GetSelfRoleListRo
  implements ResponseInterface<GetSelfRoleListResult>
{
  @ApiProperty({ example: 200 })
  code: number;

  @ApiProperty({
    enum: ApiResponseStatusEnum,
    example: ApiResponseStatusEnum.SUCCESS,
  })
  status: ApiResponseStatusEnum;

  @ApiProperty({ type: GetSelfRoleListResult })
  data: GetSelfRoleListResult;
}
