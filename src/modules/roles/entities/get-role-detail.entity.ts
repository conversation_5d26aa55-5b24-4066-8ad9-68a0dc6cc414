import { ApiProperty } from '@nestjs/swagger';
import {
  ApiResponseStatusEnum,
  LogResponseStatusEnum,
  ResponseInterface,
} from '../../../interceptors/response/response.interface';

import { customApiPropertyDescription } from '../../../utils/swagger';
export class GetRoleDetailResult {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'ID',
    }),
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '名稱',
    }),
    example: '管理員',
  })
  name: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '說明',
    }),
    example: '可以使用權限設定所有功能',
  })
  description: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '權限 Keys',
    }),
    type: [String],
    example: ['AUTHORIZATION_MANAGEMENT_ROLE_MANAGEMENT_CREATE'],
  })
  permissionActionKeys: string[];
}

export class GetRoleDetailRo implements ResponseInterface<GetRoleDetailResult> {
  @ApiProperty({ example: 200 })
  code: number;

  @ApiProperty({
    enum: LogResponseStatusEnum,
    example: LogResponseStatusEnum.SUCCESS,
  })
  logStatus: LogResponseStatusEnum;

  @ApiProperty({
    enum: ApiResponseStatusEnum,
    example: ApiResponseStatusEnum.SUCCESS,
  })
  status: ApiResponseStatusEnum;

  @ApiProperty({ type: GetRoleDetailResult })
  data: GetRoleDetailResult;
}
