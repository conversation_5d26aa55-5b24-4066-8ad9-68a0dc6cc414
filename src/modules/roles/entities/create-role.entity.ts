import { ApiProperty } from '@nestjs/swagger';
import {
  ApiResponseStatusEnum,
  LogResponseStatusEnum,
  ResponseInterface,
} from '../../../interceptors/response/response.interface';
import { customApiPropertyDescription } from '../../../utils/swagger';

export class CreateRoleResult {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'ID',
    }),
    example: 1,
  })
  id: number;
}

export class CreateRoleRo implements ResponseInterface<CreateRoleResult> {
  @ApiProperty({ example: 201 })
  code: number;

  @ApiProperty({
    enum: LogResponseStatusEnum,
    example: LogResponseStatusEnum.SUCCESS,
  })
  logStatus: LogResponseStatusEnum;

  @ApiProperty({
    enum: ApiResponseStatusEnum,
    example: ApiResponseStatusEnum.SUCCESS,
  })
  status: ApiResponseStatusEnum;

  @ApiProperty({ type: CreateRoleResult })
  data: CreateRoleResult;
}
