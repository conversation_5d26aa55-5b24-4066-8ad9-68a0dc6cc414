import { CreateOperationLogRes } from '../operation-logs/operation-logs.interface';

export enum RoleOperationTypeEnum {
  CREATE_ROLE = 'CREATE_ROLE',
  GET_ROLE_LIST = 'GET_ROLE_LIST',
  GET_ROLE_DETAIL = 'GET_ROLE_DETAIL',
  UPDATE_ROLE = 'UPDATE_ROLE',
  DELETE_ROLE = 'DELETE_ROLE',
}

export interface CreateRoleOperationLogReq {
  type: RoleOperationTypeEnum;
  isSuccess: boolean;
  operatorUserId: number;
  roleId?: number;
  roleName?: string;
  roleOriginalData?: Record<string, any>;
  inputData?: Record<string, any>;
  outputData?: Record<string, any>;
  error?: any;
}
export interface CreateRoleOperationLogRes extends CreateOperationLogRes {}

export interface GetRoleListReq {
  keyword?: string;
  size?: number;
  page?: number;
}

interface RoleCreatorOrUpdater {
  firstName: string;
  lastName: string;
  email: string;
}

export interface GetRoleListRes {
  roles: {
    id: number;
    name: string;
    description: string;
    creator: RoleCreatorOrUpdater | null;
    createdAt: number;
    updater: RoleCreatorOrUpdater | null;
    updatedAt: number;
  }[];
  totalCount: number;
}

export interface AddPoliciesToRoleReq {
  roleId: number;
  permissionActionKeys: string[];
}

export interface CreateRoleReq {
  operatorUserId: number;
  createInfo: {
    name: string;
    description: string;
    permissionActionKeys: string[];
  };
}

export interface CreateRoleRes {
  id: number;
}

export interface GetRoleListByUserIdRes {
  roleIds: number[];
  roles: {
    id: number;
    name: string;
  }[];
}

export interface GetRoleDetailRes {
  id: number;
  name: string;
  description: string;
  permissionActionKeys: string[];
}

export interface UpdateRoleReq {
  operatorUserId: number;
  roleId: number;
  updateInfo: {
    name?: string;
    description?: string;
    permissionActionKeys?: string[];
  };
}

export interface DeleteRoleReq {
  operatorUserId: number;
  roleId: number;
}

export interface GetSelfRoleListRes {
  roles: {
    id: number;
    name: string;
    description: string;
    permissionActionKeys: string[];
  }[];
}
