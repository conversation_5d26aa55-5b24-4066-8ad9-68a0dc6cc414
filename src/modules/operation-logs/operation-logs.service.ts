import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import {
  CreateSuccessfulOperationLogsReq,
  CreateFailedOperationLogsReq,
  CreateSuccessfulOperationLogsRes,
  CheckOperationEventKeysReq,
  CheckOperationEventKeysRes,
  CheckOperationKeysReq,
  CheckOperationKeysRes,
  CreateFailedOperationLogsRes,
} from './operation-logs.interface';
import { Operation } from '../../database/operation.entity';
import { OperationEvent } from '../../database/operation_event.entity';
import { OperationLog } from '../../database/operation_log.entity';

@Injectable()
export class OperationLogsService {
  constructor(
    @InjectRepository(Operation)
    private operationRepository: Repository<Operation>,
    @InjectRepository(OperationEvent)
    private operationEventRepository: Repository<OperationEvent>,
    @InjectRepository(OperationLog)
    private operationLogRepository: Repository<OperationLog>,
  ) {}

  /**
   * @description 批次新增操作成功的紀錄，會回傳寫入是否成功
   */
  public async createSuccessfulOperationLogs(
    req: CreateSuccessfulOperationLogsReq,
  ): Promise<CreateSuccessfulOperationLogsRes> {
    try {
      const operationKeys = new Set<string>();
      const operationEventKeys = new Set<string>();
      req.forEach(({ operationKey, operationEventKey }) => {
        operationKeys.add(operationKey);
        if (operationEventKey) operationEventKeys.add(operationEventKey);
      });
      await this.checkOperationKeys(Array.from(operationKeys));
      await this.checkOperationEventKeys(Array.from(operationEventKeys));

      await this.operationLogRepository.save(
        req.map(
          ({
            operationKey,
            operationEventKey,
            targetType,
            targetId,
            targetName,
            inputData,
            targetOriginalData,
            remarks,
            outputData,
            creatorUserId,
          }) => {
            return {
              operationKey,
              operationEventKey: operationEventKey || null,
              targetType,
              targetId: targetId ?? null,
              targetName: targetName ?? null,
              inputData: inputData ?? null,
              targetOriginalData: targetOriginalData ?? null,
              remarks: remarks ?? null,
              isSuccess: true,
              outputData: outputData ?? null,
              creatorUserId,
            };
          },
        ),
      );
      return {
        isSuccess: true,
      };
    } catch (error) {
      return {
        isSuccess: false,
      };
    }
  }

  /**
   * @description 批次新增操作失敗的紀錄，會回傳寫入是否成功
   */
  public async createFailedOperationLogs(
    req: CreateFailedOperationLogsReq,
  ): Promise<CreateFailedOperationLogsRes> {
    try {
      const operationKeys = new Set<string>();
      const operationEventKeys = new Set<string>();
      req.forEach(({ operationKey, operationEventKey }) => {
        operationKeys.add(operationKey);
        if (operationEventKey) operationEventKeys.add(operationEventKey);
      });
      await this.checkOperationKeys(Array.from(operationKeys));
      await this.checkOperationEventKeys(Array.from(operationEventKeys));

      await this.operationLogRepository.save(
        req.map(
          ({
            operationKey,
            operationEventKey,
            targetType,
            targetId,
            targetName,
            inputData,
            targetOriginalData,
            remarks,
            failCode,
            failReason,
            creatorUserId,
          }) => {
            return {
              operationKey,
              operationEventKey: operationEventKey || null,
              targetType,
              targetId: targetId ?? null,
              targetName: targetName ?? null,
              inputData: inputData ?? null,
              targetOriginalData: targetOriginalData ?? null,
              remarks: remarks ?? null,
              isSuccess: false,
              failCode: failCode ?? null,
              failReason: failReason ?? null,
              creatorUserId,
            };
          },
        ),
      );
      return {
        isSuccess: true,
      };
    } catch (error) {
      return {
        isSuccess: false,
      };
    }
  }

  /**
   * @description 檢查操作項目 Keys 是否存在
   */
  private async checkOperationKeys(
    req: CheckOperationKeysReq,
  ): Promise<CheckOperationKeysRes> {
    const operations = await this.operationRepository.findBy({
      key: In(req),
    });
    if (operations.length !== req.length)
      throw Error('Some operation keys do not exist');
    return operations;
  }

  /**
   * @description 檢查操作事件 Keys 是否存在
   */
  private async checkOperationEventKeys(
    req: CheckOperationEventKeysReq,
  ): Promise<CheckOperationEventKeysRes> {
    const operationEvents = await this.operationEventRepository.findBy({
      key: In(req),
    });
    if (operationEvents.length !== req.length)
      throw Error('Some operation keys do not exist');
    return operationEvents;
  }
}
