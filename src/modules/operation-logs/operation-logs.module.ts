import { Modu<PERSON> } from '@nestjs/common';
import { OperationLogsService } from './operation-logs.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OperationLog } from '../../database/operation_log.entity';
import { Operation } from '../../database/operation.entity';
import { OperationEvent } from '../../database/operation_event.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([OperationLog, Operation, OperationEvent]),
  ],
  controllers: [],
  providers: [OperationLogsService],
  exports: [OperationLogsService],
})
export class OperationLogsModule {}
