import { Operation } from '../../database/operation.entity';
import { OperationEvent } from '../../database/operation_event.entity';
import { OperationLogTargetTypeEnum } from '../../database/operation_log.entity';

export interface CreateOperationLogReq {
  operationObjectGroupKey: string;
  operationObjectKey: string;
  operationKey: string;
  operationEventKey?: string;
  targetType: OperationLogTargetTypeEnum;
  targetId?: string;
  targetName?: string;
  targetOriginalData?: Record<string, any>;
  inputData?: Record<string, any>;
  remarks?: string[];
  creatorUserId: number;
}

export interface CreateSuccessfulOperationLogReq extends CreateOperationLogReq {
  outputData?: Record<string, any>;
}

export type CreateSuccessfulOperationLogsReq =
  CreateSuccessfulOperationLogReq[];

export interface CreateFailedOperationLogReq extends CreateOperationLogReq {
  failCode?: number;
  failReason?: string;
}

export type CreateFailedOperationLogsReq = CreateFailedOperationLogReq[];

export interface CreateOperationLogRes {
  isSuccess: boolean;
}

export type CreateSuccessfulOperationLogsRes = CreateOperationLogRes;

export type CreateFailedOperationLogsRes = CreateOperationLogRes;

export type CheckOperationKeysReq = string[];

export type CheckOperationKeysRes = Operation[];

export type CheckOperationEventKeysReq = string[];

export type CheckOperationEventKeysRes = OperationEvent[];
