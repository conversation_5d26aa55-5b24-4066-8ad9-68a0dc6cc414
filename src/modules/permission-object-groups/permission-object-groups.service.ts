import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PermissionObjectGroup } from '../../database/permission_object_group.entity';
import { GetPermissionObjectGroupListRes } from './permission-object-groups.interface';

@Injectable()
export class PermissionObjectGroupsService {
  constructor(
    @InjectRepository(PermissionObjectGroup)
    private readonly permissionObjectGroupRepository: Repository<PermissionObjectGroup>,
  ) {}

  /**
   * @description 取得權限列表
   */
  public async getPermissionObjectGroupList(): Promise<GetPermissionObjectGroupListRes> {
    const permissionObjectGroups = await this.permissionObjectGroupRepository
      .createQueryBuilder('permissionObjectGroup')
      .select(['permissionObjectGroup.key', 'permissionObjectGroup.name'])
      .innerJoin('permissionObjectGroup.permissionObjects', 'permissionObject')
      .addSelect([
        'permissionObject.key',
        'permissionObject.name',
        'permissionObject.description',
      ])
      .innerJoin('permissionObject.permissionActions', 'permissionAction')
      .addSelect([
        'permissionAction.key',
        'permissionAction.name',
        'permissionAction.description',
        'permissionAction.requiredKeys',
      ])
      .addOrderBy('permissionObjectGroup.position', 'ASC')
      .addOrderBy('permissionObject.position', 'ASC')
      .addOrderBy('permissionAction.position', 'ASC')
      .getMany();

    return {
      permissionObjectGroups: permissionObjectGroups.map(
        (permissionObjectGroup) => ({
          key: permissionObjectGroup.key,
          name: permissionObjectGroup.name,
          permissionObjects:
            permissionObjectGroup.permissionObjects?.map(
              (permissionObject) => ({
                key: permissionObject.key,
                name: permissionObject.name,
                description: permissionObject.description,
                permissionActions:
                  permissionObject.permissionActions?.map(
                    (permissionAction) => ({
                      key: permissionAction.key,
                      name: permissionAction.name,
                      description: permissionAction.description,
                      requiredKeys: permissionAction.requiredKeys,
                    }),
                  ) || [],
              }),
            ) || [],
        }),
      ),
    };
  }
}
