import { GetPermissionObjectGroupListResult } from '../entities/get-permission-object-group-list.entity';

export enum PermissionObjectGroupsResponseFilterTypeEnum {
  GET_PERMISSION_OBJECT_GROUP_LIST = 'GET_PERMISSION_OBJECT_GROUP_LIST',
}

export interface PermissionObjectGroupsFilter<T, U> {
  getResponse(orig: T): U;
}

export interface GetPermissionObjectGroupListFilterPayload {
  permissionObjectGroups: {
    key: string;
    name: string;
    permissionObjects: {
      key: string;
      name: string;
      description: string;
      permissionActions: {
        key: string;
        name: string;
        description: string;
        requiredKeys: string[];
      }[];
    }[];
  }[];
}

export interface PermissionObjectGroupsResponseFilterOriginalResult {
  type: PermissionObjectGroupsResponseFilterTypeEnum;
  payload: GetPermissionObjectGroupListFilterPayload;
}

export type PermissionObjectGroupsResponseFilterResult =
  GetPermissionObjectGroupListResult;
