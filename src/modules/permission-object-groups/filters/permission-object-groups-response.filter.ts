import { Injectable } from '@nestjs/common';
import {
  GetPermissionObjectGroupListFilterPayload,
  PermissionObjectGroupsFilter,
  PermissionObjectGroupsResponseFilterOriginalResult,
  PermissionObjectGroupsResponseFilterResult,
  PermissionObjectGroupsResponseFilterTypeEnum,
} from './permission-object-groups.filter.interface';
import { GetPermissionObjectGroupListResult } from '../entities/get-permission-object-group-list.entity';

@Injectable()
export class PermissionObjectGroupsResponseFilter
  implements
    PermissionObjectGroupsFilter<
      PermissionObjectGroupsResponseFilterOriginalResult,
      PermissionObjectGroupsResponseFilterResult
    >
{
  getResponse(
    orig: PermissionObjectGroupsResponseFilterOriginalResult,
  ): PermissionObjectGroupsResponseFilterResult {
    switch (orig.type) {
      case PermissionObjectGroupsResponseFilterTypeEnum.GET_PERMISSION_OBJECT_GROUP_LIST: {
        const payload =
          orig.payload as GetPermissionObjectGroupListFilterPayload;
        let result = new GetPermissionObjectGroupListResult();
        result = { ...payload };
        return result;
      }
    }
  }
}
