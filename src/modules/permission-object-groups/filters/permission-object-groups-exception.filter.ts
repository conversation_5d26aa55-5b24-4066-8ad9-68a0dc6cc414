import { HttpException, HttpStatus } from '@nestjs/common';
import { PermissionObjectGroupsFilter } from './permission-object-groups.filter.interface';

export class PermissionObjectGroupsExceptionFilter
  implements PermissionObjectGroupsFilter<Error, HttpException>
{
  getResponse({ message: originalErrorMessage }: Error): HttpException {
    let errorMessage: string;
    let statusCode: number;

    switch (originalErrorMessage) {
      default:
        errorMessage = originalErrorMessage;
        statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
    }

    return new HttpException(
      {
        message: errorMessage,
      },
      statusCode,
    );
  }
}
