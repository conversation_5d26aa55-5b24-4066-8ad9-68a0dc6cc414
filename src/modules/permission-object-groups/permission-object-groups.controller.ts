import { Controller, Get, UseGuards } from '@nestjs/common';
import { Api<PERSON><PERSON>erA<PERSON>, ApiOkResponse, ApiOperation } from '@nestjs/swagger';
import { PermissionObjectGroupsService } from './permission-object-groups.service';
import UserAuthGuard from '../auth/guards/user-auth.guard';
import { PermissionObjectGroupsResponseFilter } from './filters/permission-object-groups-response.filter';
import { PermissionObjectGroupsExceptionFilter } from './filters/permission-object-groups-exception.filter';
import { PermissionObjectGroupsResponseFilterTypeEnum } from './filters/permission-object-groups.filter.interface';
import {
  GetPermissionObjectGroupListResult,
  GetPermissionObjectGroupListRo,
} from './entities/get-permission-object-group-list.entity';

@ApiBearerAuth()
@UseGuards(UserAuthGuard)
@Controller('permission-object-groups')
export class PermissionObjectGroupsController {
  constructor(
    private readonly permissionObjectGroupsService: PermissionObjectGroupsService,
    private readonly permissionObjectGroupsResponseFilter: PermissionObjectGroupsResponseFilter,
    private readonly permissionObjectGroupsExceptionFilter: PermissionObjectGroupsExceptionFilter,
  ) {}

  @Get()
  @ApiOperation({
    summary: '取得權限列表',
  })
  @ApiOkResponse({
    type: GetPermissionObjectGroupListRo,
  })
  async getPermissionObjectGroupList(): Promise<GetPermissionObjectGroupListResult> {
    try {
      const res =
        await this.permissionObjectGroupsService.getPermissionObjectGroupList();
      const result = this.permissionObjectGroupsResponseFilter.getResponse({
        type: PermissionObjectGroupsResponseFilterTypeEnum.GET_PERMISSION_OBJECT_GROUP_LIST,
        payload: res,
      }) as GetPermissionObjectGroupListResult;
      return result;
    } catch (error) {
      throw this.permissionObjectGroupsExceptionFilter.getResponse(error);
    }
  }
}
