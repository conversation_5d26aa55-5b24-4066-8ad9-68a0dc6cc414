export interface GetPermissionObjectGroupListRes {
  permissionObjectGroups: {
    key: string;
    name: string;
    permissionObjects: {
      key: string;
      name: string;
      description: string;
      permissionActions: {
        key: string;
        name: string;
        requiredKeys: string[];
        description: string;
      }[];
    }[];
  }[];
}

/* eslint-disable @typescript-eslint/no-namespace */
export namespace PermissionConfig {
  export namespace AUTHORIZATION_MANAGEMENT {
    export const permissionObjectGroupKey = 'AUTHORIZATION_MANAGEMENT';
    export const permissionObjectGroupName = '權限管理';
    export const permissionObjectGroupPosition = 1;

    export namespace ROLE_MANAGEMENT {
      export const permissionObjectKey = `${permissionObjectGroupKey}_ROLE_MANAGEMENT`;
      export const permissionObjectName = '角色管理';
      export const permissionObjectDescription = '角色設定';
      export const permissionObjectPosition = 1;

      export const READ = {
        permissionObjectKey,
        permissionActionKey: `${permissionObjectKey}_READ`,
        permissionActionName: '檢視',
        permissionActionPosition: 1,
        permissionActionDescription: '檢視角色，包含列表、單一角色詳細資訊',
      };

      export const CREATE = {
        permissionObjectKey,
        permissionActionKey: `${permissionObjectKey}_CREATE`,
        permissionActionName: '新增',
        permissionActionPosition: 2,
        permissionActionDescription: '新增角色',
        permissionRequiredActionKeys: [
          PermissionConfig.AUTHORIZATION_MANAGEMENT.ROLE_MANAGEMENT.READ
            .permissionActionKey,
        ],
      };

      export const UPDATE = {
        permissionObjectKey,
        permissionActionKey: `${permissionObjectKey}_UPDATE`,
        permissionActionName: '編輯',
        permissionActionPosition: 3,
        permissionActionDescription: '編輯角色',
        permissionRequiredActionKeys: [
          PermissionConfig.AUTHORIZATION_MANAGEMENT.ROLE_MANAGEMENT.READ
            .permissionActionKey,
        ],
      };

      export const DELETE = {
        permissionObjectKey,
        permissionActionKey: `${permissionObjectKey}_DELETE`,
        permissionActionName: '刪除',
        permissionActionPosition: 4,
        permissionActionDescription: '刪除角色',
        permissionRequiredActionKeys: [
          PermissionConfig.AUTHORIZATION_MANAGEMENT.ROLE_MANAGEMENT.READ
            .permissionActionKey,
        ],
      };
    }

    export namespace USER_MANAGEMENT {
      export const permissionObjectKey = `${permissionObjectGroupKey}_USER_MANAGEMENT`;
      export const permissionObjectName = '使用者管理';
      export const permissionObjectDescription = '使用者設定';
      export const permissionObjectPosition = 1;

      export const READ = {
        permissionObjectKey,
        permissionActionKey: `${permissionObjectKey}_READ`,
        permissionActionName: '檢視',
        permissionActionPosition: 1,
        permissionActionDescription: '檢視使用者，包含列表、單一使用者資料',
      };

      export const CREATE = {
        permissionObjectKey,
        permissionActionKey: `${permissionObjectKey}_CREATE`,
        permissionActionName: '新增',
        permissionActionPosition: 2,
        permissionActionDescription: '新增使用者',
        permissionRequiredActionKeys: [
          PermissionConfig.AUTHORIZATION_MANAGEMENT.USER_MANAGEMENT.READ
            .permissionActionKey,
        ],
      };

      export const UPDATE = {
        permissionObjectKey,
        permissionActionKey: `${permissionObjectKey}_UPDATE`,
        permissionActionName: '編輯/停用/啟用',
        permissionActionPosition: 3,
        permissionActionDescription: '編輯使用者，包含停用、啟用使用者',
        permissionRequiredActionKeys: [
          PermissionConfig.AUTHORIZATION_MANAGEMENT.USER_MANAGEMENT.READ
            .permissionActionKey,
        ],
      };

      export const DELETE = {
        permissionObjectKey,
        permissionActionKey: `${permissionObjectKey}_DELETE`,
        permissionActionName: '刪除',
        permissionActionPosition: 4,
        permissionActionDescription: '刪除使用者',
        permissionRequiredActionKeys: [
          PermissionConfig.AUTHORIZATION_MANAGEMENT.USER_MANAGEMENT.READ
            .permissionActionKey,
        ],
      };
    }
  }
  export namespace TERMS_MANAGEMENT {
    export const permissionObjectGroupKey = 'TERMS_MANAGEMENT';
    export const permissionObjectGroupName = '條款管理';
    export const permissionObjectGroupPosition = 2;

    export namespace TERMS_LIST {
      export const permissionObjectKey = `${permissionObjectGroupKey}_TERMS_LIST`;
      export const permissionObjectName = '條款列表';
      export const permissionObjectDescription = '條款清單';
      export const permissionObjectPosition = 1;

      export const READ = {
        permissionObjectKey,
        permissionActionKey: `${permissionObjectKey}_READ`,
        permissionActionName: '檢視',
        permissionActionPosition: 1,
        permissionActionDescription: '檢視條款列表',
      };

      export const CREATE_VERSION = {
        permissionObjectKey,
        permissionActionKey: `${permissionObjectKey}_CREATE_VERSION`,
        permissionActionName: '新增',
        permissionActionPosition: 2,
        permissionActionDescription: '新增條款版本',
        permissionRequiredActionKeys: [
          PermissionConfig.TERMS_MANAGEMENT.TERMS_LIST.READ.permissionActionKey,
        ],
      };
    }
  }
}
