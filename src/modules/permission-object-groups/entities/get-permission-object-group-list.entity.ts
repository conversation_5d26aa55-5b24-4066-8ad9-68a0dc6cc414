import { ApiProperty } from '@nestjs/swagger';
import { customApiPropertyDescription } from '../../../utils/swagger';
import {
  ApiResponseStatusEnum,
  ResponseInterface,
} from '../../../interceptors/response/response.interface';

class PermissionAction {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '操作行為 Key',
    }),
    example: 'AUTHORIZATION_MANAGEMENT_USER_MANAGEMENT_CREATE',
  })
  key: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '操作行為名稱',
    }),
    example: '新增使用者',
  })
  name: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '需要有的操作行為 Keys',
    }),
    example: ['AUTHORIZATION_MANAGEMENT_ROLE_MANAGEMENT_READ'],
  })
  requiredKeys: string[];

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '操作行為描述',
    }),
    example: '描述',
  })
  description: string;
}

class PermissionObject {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '功能 Key',
    }),
    example: 'AUTHORIZATION_MANAGEMENT_USER_MANAGEMENT',
  })
  key: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '功能名稱',
    }),
    example: '使用者設定',
  })
  name: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '功能描述',
    }),
    example: '描述',
  })
  description: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '與功能有關的操作行為',
      remarks: ['對應功能操作行為'],
    }),
    type: [PermissionAction],
  })
  permissionActions: PermissionAction[];
}

class PermissionObjectGroup {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '群組 Key',
    }),
    example: 'AUTH',
  })
  key: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '群組名稱',
    }),
    example: '權限管理',
  })
  name: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '與群組有關的功能',
      remarks: ['對應子功能'],
    }),
    type: [PermissionObject],
  })
  permissionObjects: PermissionObject[];
}

export class GetPermissionObjectGroupListResult {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '群組列表',
      remarks: ['對應功能'],
    }),
    type: [PermissionObjectGroup],
  })
  permissionObjectGroups: PermissionObjectGroup[];
}

export class GetPermissionObjectGroupListRo
  implements ResponseInterface<GetPermissionObjectGroupListResult>
{
  @ApiProperty({ example: 200 })
  code: number;

  @ApiProperty({
    enum: ApiResponseStatusEnum,
    example: ApiResponseStatusEnum.SUCCESS,
  })
  status: ApiResponseStatusEnum;

  @ApiProperty({ type: GetPermissionObjectGroupListResult })
  data: GetPermissionObjectGroupListResult;
}
