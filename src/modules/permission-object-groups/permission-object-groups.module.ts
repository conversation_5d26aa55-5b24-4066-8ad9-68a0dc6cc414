import { Module } from '@nestjs/common';
import { PermissionObjectGroupsService } from './permission-object-groups.service';
import { PermissionObjectGroupsController } from './permission-object-groups.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PermissionObjectGroupsResponseFilter } from './filters/permission-object-groups-response.filter';
import { PermissionObjectGroupsExceptionFilter } from './filters/permission-object-groups-exception.filter';
import { PermissionAction } from '../../database/permission_action.entity';
import { PermissionObject } from '../../database/permission_object.entity';
import { PermissionObjectGroup } from '../../database/permission_object_group.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      PermissionAction,
      PermissionObject,
      PermissionObjectGroup,
    ]),
  ],
  controllers: [PermissionObjectGroupsController],
  providers: [
    PermissionObjectGroupsService,
    PermissionObjectGroupsResponseFilter,
    PermissionObjectGroupsExceptionFilter,
  ],
  exports: [],
})
export class PermissionObjectGroupsModule {}
