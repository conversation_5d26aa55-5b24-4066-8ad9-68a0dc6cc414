import { Injectable } from '@nestjs/common';
import * as sgMail from '@sendgrid/mail';
import { ConfigService } from '@nestjs/config';
import { SendMailReq } from './mail.interface';

@Injectable()
export class MailService {
  constructor(private configService: ConfigService) {
    sgMail.setApiKey(configService.get('sendMail.apiKey'));
  }

  public async sendMail({ to, mailTemplate }: SendMailReq): Promise<void> {
    await sgMail.send({
      to,
      from: {
        email: this.configService.get('sendMail.sanderEmail'),
        name: this.configService.get('sendMail.sanderName'),
      },
      subject: mailTemplate.subject,
      html: await mailTemplate.getHtmlContent(),
    });
  }
}
