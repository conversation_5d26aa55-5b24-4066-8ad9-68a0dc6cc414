import { promises as fs } from 'fs';
import * as Handlebars from 'handlebars';

export abstract class MailTemplate {
  abstract subject: string;

  abstract templatePath: string;

  data: Record<any, any> = {};

  public async getHtmlContent() {
    const templateSource = await fs.readFile(this.templatePath, {
      encoding: 'utf-8',
    });
    const html = Handlebars.compile(templateSource)(this.data);
    return html;
  }
}
