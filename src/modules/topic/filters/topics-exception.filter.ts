import { HttpException, HttpStatus } from '@nestjs/common';
import { TopicAdminFilter } from 'src/modules/topic/filters/topic.filter.interface';

export class TopicsExceptionFilter
  implements TopicAdminFilter<Error, HttpException>
{
  getResponse({ message: originalErrorMessage }: Error): HttpException {
    switch (originalErrorMessage) {
      case 'Topic not found.':
        return new HttpException('查無主題', HttpStatus.NOT_FOUND);
      default:
        return new HttpException(
          originalErrorMessage || 'Unexpected error occurred',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
    }
  }
}
