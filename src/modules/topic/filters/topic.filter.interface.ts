import { GetAllTopicsResult } from 'src/modules/topic/entities/get-all-topic-result.entity';
import { GetTopicResult } from 'src/modules/topic/entities/get-topic-result.entity';
import { PaginationResponseFilterPayload } from 'src/shared/filters/pagination.filter.interface';
import { OperatorRes } from 'src/shared/interfaces/operator.interface';

export enum TopicResponseFilterTypeEnum {
  GET_ALL_TOPIC = 'GET_ALL_TOPIC',
  GET_TOPIC_DETAIL = 'GET_TOPIC_DETAIL',
  CREATE_TOPIC = 'CREATE_TOPIC',
  UPDATE_TOPIC = 'UPDATE_TOPIC',
}

export interface TopicResponseAdminFilterPayload {
  id: number;
  enName: string;
  zhTwName?: string;
  createdAt: number;
  updatedAt: number;
  deletedAt?: number;
  creator: OperatorRes;
  updater: OperatorRes;
  deleter?: OperatorRes;
}

export interface TopicResponseFilterPayload {
  id: number;
  name: string;
}

export interface GetAllTopicsResponseFilterPayload
  extends PaginationResponseFilterPayload<TopicResponseAdminFilterPayload> {
  data: TopicResponseAdminFilterPayload[];
}

export interface TopicAdminFilter<T, U> {
  getResponse(orig: T): U;
}

export interface TopicResponseFilterOriginalResult {
  type: TopicResponseFilterTypeEnum;
  payload: TopicResponseAdminFilterPayload | GetAllTopicsResponseFilterPayload;
}

export type TopicResponseFilterResult = GetAllTopicsResult | GetTopicResult;
