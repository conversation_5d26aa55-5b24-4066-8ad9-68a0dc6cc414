import { Injectable } from '@nestjs/common';
import {
  GetAllTopicsResponseFilterDto,
  TopicAdminFilter,
  TopicResponseFilterOriginalResult,
  TopicResponseFilterResult,
  TopicResponseFilterTypeEnum,
} from 'src/modules/topic/filters';
import { TopicResponseAdminFilterPayload } from 'src/modules/topic/filters/topic.filter.interface';
import { GetAllTopicsResult } from 'src/modules/topic/entities/get-all-topic-result.entity';
import { GetTopicResult } from 'src/modules/topic/entities/get-topic-result.entity';

@Injectable()
export class TopicResponseFilter
  implements
    TopicAdminFilter<
      TopicResponseFilterOriginalResult,
      TopicResponseFilterResult
    >
{
  getResponse(
    orig: TopicResponseFilterOriginalResult,
  ): TopicResponseFilterResult {
    switch (orig.type) {
      case TopicResponseFilterTypeEnum.GET_ALL_TOPIC: {
        const payload = orig.payload as GetAllTopicsResponseFilterDto;
        const result = new GetAllTopicsResult();
        result.data = payload.data.map((topic) => ({
          id: topic.id,
          enName: topic.enName,
          zhTwName: topic.zhTwName ?? undefined,
          createdAt: topic.createdAt,
          updatedAt: topic.updatedAt,
          deletedAt: topic.deletedAt,
          creator: topic.creator,
          updater: topic.updater,
          deleter: topic.deleter,
        }));
        result.totalCount = payload.totalCount;
        result.totalPage = payload.totalPage;
        result.currentPage = payload.currentPage;
        result.size = payload.size;
        return result;
      }

      case TopicResponseFilterTypeEnum.UPDATE_TOPIC:
      case TopicResponseFilterTypeEnum.CREATE_TOPIC:
      case TopicResponseFilterTypeEnum.GET_TOPIC_DETAIL: {
        const payload = orig.payload as TopicResponseAdminFilterPayload;
        const result = new GetTopicResult();
        result.id = payload.id;
        result.enName = payload.enName;
        result.zhTwName = payload.zhTwName;
        result.createdAt = payload.createdAt;
        result.updatedAt = payload.updatedAt;
        result.deletedAt = payload.deletedAt;
        result.creator = payload.creator;
        result.updater = payload.updater;
        result.deleter = payload.deleter;

        return result;
      }
    }
  }
}
