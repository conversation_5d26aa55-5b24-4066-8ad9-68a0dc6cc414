import {
  Body,
  Controller,
  Delete,
  Get,
  HttpException,
  HttpStatus,
  InternalServerErrorException,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import UserAuthGuard from 'src/modules/auth/guards/user-auth.guard';
import {
  CreateTopicDto,
  GetTopicByIdDto,
  GetTopicsDto,
  UpdateTopicDto,
} from 'src/modules/topic/dto';
import { TopicService } from 'src/modules/topic/topic.service';

import { ApiResultAndCreateOperationLogResult } from 'src/interceptors/response/response.interface';
import { GetAllTopicsResult } from 'src/modules/topic/entities/get-all-topic-result.entity';
import { GetTopicResult } from 'src/modules/topic/entities/get-topic-result.entity';
import {
  TopicResponseFilter,
  TopicResponseFilterTypeEnum,
} from 'src/modules/topic/filters';
import { TopicsExceptionFilter } from 'src/modules/topic/filters/topics-exception.filter';
import { customApiResponseDescription } from 'src/utils/swagger';

@ApiTags('Admin Topics')
@Controller('admin/topics')
@UseGuards(UserAuthGuard)
@ApiBearerAuth()
export class AdminTopicController {
  constructor(
    private readonly topicService: TopicService,
    private readonly topicResponseFilter: TopicResponseFilter,
    private readonly topicsExceptionFilter: TopicsExceptionFilter,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Get all topics',
    description: 'Retrieve all topics with pagination and filtering options',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: customApiResponseDescription({
      basic: 'Topics retrieved successfully',
      remarks: [
        'Supports pagination and search filtering',
        'Excludes deleted topics by default',
        'Can include page counts for each topic',
      ],
    }),
    type: GetAllTopicsResult,
  })
  async getAllTopics(
    @Query() query: GetTopicsDto,
  ): Promise<ApiResultAndCreateOperationLogResult<GetAllTopicsResult>> {
    try {
      const res = await this.topicService.findAllTopics(query);
      const result = this.topicResponseFilter.getResponse({
        type: TopicResponseFilterTypeEnum.GET_ALL_TOPIC,
        payload: res,
      }) as GetAllTopicsResult;
      return { result };
    } catch (error) {
      throw this.topicsExceptionFilter.getResponse(error);
    }
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get topic by ID',
    description: 'Retrieve a specific topic by its ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Topic ID',
    type: 'number',
    example: 1,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: customApiResponseDescription({
      basic: 'Topic retrieved successfully',
    }),
    type: GetTopicResult,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: customApiResponseDescription({
      basic: 'Topic not found',
    }),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: customApiResponseDescription({
      basic: 'Invalid topic ID format',
    }),
  })
  async getTopicById(
    @Param() params: GetTopicByIdDto,
  ): Promise<ApiResultAndCreateOperationLogResult<GetTopicResult>> {
    try {
      const res = await this.topicService.findTopicById(params.id);
      const result = this.topicResponseFilter.getResponse({
        type: TopicResponseFilterTypeEnum.GET_TOPIC_DETAIL,
        payload: res,
      }) as GetTopicResult;
      return { result };
    } catch (error) {
      throw this.topicsExceptionFilter.getResponse(error);
    }
  }

  @Post()
  @ApiOperation({
    summary: 'Create new topic',
    description: 'Create a new topic for categorizing pages',
  })
  @ApiBody({
    type: CreateTopicDto,
    description: 'Topic creation data',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: customApiResponseDescription({
      basic: 'Topic created successfully',
    }),
    type: GetTopicResult,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: customApiResponseDescription({
      basic: 'Invalid request data',
      remarks: ['Validation errors in topic data'],
    }),
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: customApiResponseDescription({
      basic: 'Topic name already exists',
      remarks: ['Topic names must be unique'],
    }),
  })
  async createTopic(
    @Req() req,
    @Body() createTopicDto: CreateTopicDto,
  ): Promise<ApiResultAndCreateOperationLogResult<GetTopicResult>> {
    try {
      const operationUserId = req.user.userId;
      const res = await this.topicService.createTopic(
        createTopicDto,
        operationUserId,
      );
      const result = this.topicResponseFilter.getResponse({
        type: TopicResponseFilterTypeEnum.CREATE_TOPIC,
        payload: res,
      }) as GetTopicResult;
      return { result };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      } else {
        throw new InternalServerErrorException(
          error?.message || 'Unexpected error occurred',
        );
      }
    }
  }

  @Put(':id')
  @ApiOperation({
    summary: 'Update topic',
    description: 'Update an existing topic',
  })
  @ApiParam({
    name: 'id',
    description: 'Topic ID',
    type: 'number',
    example: 1,
  })
  @ApiBody({
    type: UpdateTopicDto,
    description: 'Topic update data',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: customApiResponseDescription({
      basic: 'Topic updated successfully',
    }),
    type: GetTopicResult,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: customApiResponseDescription({
      basic: 'Topic not found',
    }),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: customApiResponseDescription({
      basic: 'Invalid request data',
    }),
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: customApiResponseDescription({
      basic: 'Topic name already exists',
    }),
  })
  async updateTopic(
    @Req() req,
    @Param() params: GetTopicByIdDto,
    @Body() updateTopicDto: UpdateTopicDto,
  ): Promise<ApiResultAndCreateOperationLogResult<GetTopicResult>> {
    try {
      const operationUserId = req.user.userId;
      const res = await this.topicService.updateTopic(
        params.id,
        updateTopicDto,
        operationUserId,
      );
      const result = this.topicResponseFilter.getResponse({
        type: TopicResponseFilterTypeEnum.UPDATE_TOPIC,
        payload: res,
      }) as GetTopicResult;
      return { result };
    } catch (error) {
      throw this.topicsExceptionFilter.getResponse(error);
    }
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete topic',
    description: 'Soft delete a topic and its page associations',
  })
  @ApiParam({
    name: 'id',
    description: 'Topic ID',
    type: 'number',
    example: 1,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: customApiResponseDescription({
      basic: 'Topic deleted successfully',
      remarks: [
        'Soft delete - topic is marked as deleted',
        'Associated page relationships are also deleted',
      ],
    }),
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: customApiResponseDescription({
      basic: 'Topic not found',
    }),
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: customApiResponseDescription({
      basic: 'Invalid topic ID format',
    }),
  })
  async deleteTopic(
    @Req() req,
    @Param() params: GetTopicByIdDto,
  ): Promise<void> {
    try {
      const operationUserId = req.user.userId;
      return this.topicService.deleteTopic(params.id, operationUserId);
    } catch (error) {
      throw this.topicsExceptionFilter.getResponse(error);
    }
  }
}
