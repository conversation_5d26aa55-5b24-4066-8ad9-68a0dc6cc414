import { Controller, Get, HttpStatus } from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { TopicService } from 'src/modules/topic/topic.service';
import {
  TopicResponseFilter,
  TopicResponseFilterResult,
  TopicResponseFilterTypeEnum,
} from 'src/modules/topic/filters';
import { TopicsExceptionFilter } from 'src/modules/topic/filters/topics-exception.filter';
import { GetAllTopicsResult } from 'src/modules/topic/entities/get-all-topic-result.entity';

@Controller('topic')
export class TopicController {
  constructor(
    private readonly topicService: TopicService,
    private readonly topicResponseFilter: TopicResponseFilter,
    private readonly topicsExceptionFilter: TopicsExceptionFilter,
  ) {}

  @Get('main')
  @ApiOperation({
    summary: 'Get main topics with must have page ',
    description: 'Retrieve all main topics with must have page',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    type: GetAllTopicsResult,
  })
  async getListMainTopic(): Promise<TopicResponseFilterResult> {
    try {
      const res = await this.topicService.findAllTopics(
        {
          page: 1,
          size: 100,
          keywords: null,
        },
        true,
      );
      const result = this.topicResponseFilter.getResponse({
        type: TopicResponseFilterTypeEnum.GET_ALL_TOPIC,
        payload: res,
      });
      return result;
    } catch (error) {
      throw this.topicsExceptionFilter.getResponse(error);
    }
  }
}
