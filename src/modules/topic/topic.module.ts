import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdminTopicController } from 'src/modules/topic/controllers/admin-topic.controller';
import { PageTopic } from 'src/database/page_topic.entity';
import { Topic } from 'src/database/topic.entity';
import { TopicResponseFilter } from 'src/modules/topic/filters';
import { TopicService } from 'src/modules/topic/topic.service';
import { TranslationModule } from 'src/modules/translation/translation.module';
import { Page } from 'src/database/page.entity';
import { TopicsExceptionFilter } from 'src/modules/topic/filters/topics-exception.filter';
import { PageModule } from 'src/modules/page/page.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Topic, PageTopic, Page]),
    TranslationModule,
    PageModule,
  ],
  controllers: [AdminTopicController],
  providers: [TopicService, TopicResponseFilter, TopicsExceptionFilter],
  exports: [TopicService],
})
export class TopicModule {}
