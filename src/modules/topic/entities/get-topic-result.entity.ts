import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { OperatorResult } from 'src/shared/entities/operator-result.entity';
import { customApiPropertyDescription } from 'src/utils/swagger';

export class GetTopicResult {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'Topic ID',
    }),
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'Topic name',
      remarks: ['Used for categorizing and filtering pages'],
    }),
    example: '3D Rendering',
  })
  enName: string;

  @ApiPropertyOptional({
    description: customApiPropertyDescription({
      basic: 'CN Topic name',
      remarks: ['Used for categorizing and filtering pages'],
    }),
    example: '3D Rendering',
  })
  zhTwName: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'Creation timestamp',
    }),
    example: 1703097600000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Creator user',
    type: OperatorResult,
  })
  creator: OperatorR<PERSON>ult;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'Last update timestamp',
    }),
    example: 1703097600000,
  })
  updatedAt: number;

  @ApiProperty({
    description: 'Updater user',
    type: OperatorResult,
  })
  updater: OperatorResult;

  @ApiPropertyOptional({
    description: customApiPropertyDescription({
      basic: 'Deletion timestamp',
      remarks: ['Present only for soft-deleted topics'],
    }),
    example: null,
  })
  deletedAt?: number;

  @ApiPropertyOptional({
    description: 'Deleter user',
    type: OperatorResult,
    required: false,
  })
  deleter?: OperatorResult;
}
