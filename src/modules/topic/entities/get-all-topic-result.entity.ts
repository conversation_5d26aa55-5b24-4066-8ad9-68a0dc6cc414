import { ApiProperty } from '@nestjs/swagger';
import { GetTopicResult } from 'src/modules/topic/entities/get-topic-result.entity';
import { PaginationResult } from 'src/shared/entities/pagination-result.entity';
import { customApiPropertyDescription } from 'src/utils/swagger';

export class GetAllTopicsResult extends PaginationResult<GetTopicResult> {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'List of topics',
      remarks: [
        'Ordered by creation date DESC',
        'Excludes deleted topics by default',
      ],
    }),
    type: [GetTopicResult],
  })
  data: GetTopicResult[];
}
