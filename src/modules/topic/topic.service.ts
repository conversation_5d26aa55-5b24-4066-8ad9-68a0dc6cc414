import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Page, PageStatusEnum, PageTypeEnum } from 'src/database/page.entity';
import { PageTopic } from 'src/database/page_topic.entity';

import {
  CreateTopicDto,
  GetTopicsDto,
  UpdateTopicDto,
} from 'src/modules/topic/dto';
import { GetAllTopicsResponseFilterDto } from 'src/modules/topic/filters';
import { TopicResponseAdminFilterPayload } from 'src/modules/topic/filters/topic.filter.interface';
import { TranslationService } from 'src/modules/translation/services/translation.services';

import {
  Topic,
  TOPIC_DATA_TRANSLATION_FIELD_NAME,
  TOPIC_TABLE_NAME,
} from 'src/database/topic.entity';
import { LanguageCodeEnum } from 'src/database/translation.entity';
import { PageAdminService } from 'src/modules/page/services/page-admin.service';
import { mapUserToOperatorResponseDto } from 'src/shared/interfaces/operator.interface';
import { PaginationRes } from 'src/shared/interfaces/pagination.interface';
import { Repository } from 'typeorm';

@Injectable()
export class TopicService {
  constructor(
    @InjectRepository(Topic)
    private readonly topicRepository: Repository<Topic>,
    @InjectRepository(PageTopic)
    private readonly topicPageRepository: Repository<PageTopic>,
    @InjectRepository(Page)
    private readonly pageRepository: Repository<Page>,
    private readonly translationService: TranslationService,
    private readonly pageAdminService: PageAdminService,
  ) {}

  /**
   * Create a new topic
   * @param createTopicDto - Topic creation data
   * @param userId - ID of the user creating the topic
   * @returns Created topic
   * @throws ConflictException if topic name already exists
   */
  async createTopic(
    createTopicDto: CreateTopicDto,
    userId: number,
  ): Promise<TopicResponseAdminFilterPayload> {
    const enName = createTopicDto.name[LanguageCodeEnum.EN];
    const zhTwName = createTopicDto.name[LanguageCodeEnum.ZHTW];
    const uniqueEnValue = `${LanguageCodeEnum.EN}_${enName}`;
    const uniqueZhTwValue = `${LanguageCodeEnum.ZHTW}_${zhTwName}`;
    const uniqueValue = [uniqueEnValue, uniqueZhTwValue];
    const existingTranslations =
      await this.translationService.getTranslationsByUniqueValue(
        uniqueValue,
        TOPIC_DATA_TRANSLATION_FIELD_NAME,
        TOPIC_TABLE_NAME,
      );

    if (existingTranslations && existingTranslations.length > 0) {
      throw new ConflictException(`Topic already exists`);
    }

    const topic = this.topicRepository.create({
      name: enName,
      creatorUserId: userId,
      updaterUserId: userId,
    });

    const savedTopic = await this.topicRepository.save(topic);

    await this.translationService.createTranslationById(
      savedTopic.id,
      TOPIC_TABLE_NAME,
      userId,
      enName,
      TOPIC_DATA_TRANSLATION_FIELD_NAME,
      LanguageCodeEnum.EN,
      uniqueEnValue,
    );
    //save to translation table
    await this.translationService.createTranslationById(
      savedTopic.id,
      TOPIC_TABLE_NAME,
      userId,
      zhTwName,
      TOPIC_DATA_TRANSLATION_FIELD_NAME,
      LanguageCodeEnum.ZHTW,
      uniqueZhTwValue,
    );

    return this.mapTopicToResponseFilterDto(savedTopic);
  }

  /**
   * Find all topics with pagination and filtering
   * @param query - Query parameters for filtering and pagination
   * @param isWithPage - Whether to include page with topic
   * @returns Paginated list of topics
   */
  async findAllTopics(
    query: GetTopicsDto,
    isWithPage: boolean = false,
  ): Promise<GetAllTopicsResponseFilterDto> {
    const { page = 1, size = 30, keywords } = query;

    const qb = this.topicRepository.createQueryBuilder(TOPIC_TABLE_NAME);

    // Filter conditions

    if (keywords) {
      const keywordConditions = keywords.map(
        (keyword) => `LOWER(topic.name) LIKE LOWER(:keyword${keyword})`,
      );
      const keywordParams = keywords.reduce(
        (acc, keyword) => ({
          ...acc,
          [`keyword${keyword}`]: `%${keyword}%`,
        }),
        {},
      );

      qb.andWhere(`(${keywordConditions.join(' OR ')})`, keywordParams);
    }

    if (isWithPage) {
      qb.leftJoin(PageTopic, 'pageTopic', 'pageTopic.topicId = topic.id');
      qb.leftJoin(Page, 'page', 'page.id = pageTopic.pageId');
      qb.where('page.deletedAt IS NULL');
      qb.andWhere('page.status = :status', {
        status: PageStatusEnum.PUBLISHED,
      });
      qb.andWhere('page.pageType = :pageType', {
        pageType: PageTypeEnum.CASE_STUDY_LIST,
      });
    }

    // Pagination
    const offset = (page - 1) * size;
    qb.skip(offset).take(size);

    // Order by creation date DESC
    qb.orderBy('topic.createdAt', 'DESC');

    const [topics, total] = await qb.getManyAndCount();
    const topicIds = topics.map((h) => h.id);
    const translations = await this.translationService.getTranslationsByTable(
      topicIds,
      TOPIC_TABLE_NAME,
    );

    // Get page counts separately if needed (more efficient for large datasets)
    const topicsWithPageCount = topics.map((topic) => {
      const zhTranslation = translations.find(
        (t) =>
          t.languageCode === LanguageCodeEnum.ZHTW && t.resourceId === topic.id,
      );

      return this.mapTopicToResponseFilterDto(topic, zhTranslation?.value);
    });

    return PaginationRes.create(topicsWithPageCount, total, page, size);
  }

  /**
   * Find topic by ID
   * @param id - Topic ID
   * @param includePageCount - Whether to include page count
   * @returns Topic information
   * @throws NotFoundException if topic doesn't exist
   */
  async findTopicById(id: number): Promise<TopicResponseAdminFilterPayload> {
    const topic = await this.topicRepository.findOne({
      where: { id, deletedAt: null },
    });

    if (!topic) {
      throw new NotFoundException(`Topic with ID ${id} not found`);
    }
    const translations = await this.translationService.getTranslationsByTable(
      id,
      TOPIC_TABLE_NAME,
    );

    const zhTranslation = translations.find(
      (t) => t.languageCode === LanguageCodeEnum.ZHTW,
    );

    const responseDto = this.mapTopicToResponseFilterDto(
      topic,
      zhTranslation.value,
    );

    return responseDto;
  }

  /**
   * Update topic
   * @param id - Topic ID
   * @param updateTopicDto - Topic update data
   * @param userId - ID of the user updating the topic
   * @returns Updated topic
   * @throws NotFoundException if topic doesn't exist
   * @throws ConflictException if new name already exists
   */
  async updateTopic(
    id: number,
    updateTopicDto: UpdateTopicDto,
    userId: number,
  ): Promise<TopicResponseAdminFilterPayload> {
    const enName = updateTopicDto.name[LanguageCodeEnum.EN];
    const zhTwName = updateTopicDto.name[LanguageCodeEnum.ZHTW];
    const uniqueEnValue = `${LanguageCodeEnum.EN}_${enName}`;
    const uniqueZhTwValue = `${LanguageCodeEnum.ZHTW}_${zhTwName}`;
    const uniqueValue = [uniqueEnValue, uniqueZhTwValue];
    const existingTranslations =
      await this.translationService.getTranslationsByUniqueValue(
        uniqueValue,
        TOPIC_DATA_TRANSLATION_FIELD_NAME,
        TOPIC_TABLE_NAME,
      );

    if (existingTranslations && existingTranslations.length > 0) {
      throw new ConflictException(`Topic already exists`);
    }

    const topic = await this.topicRepository.findOne({
      where: { id, deletedAt: null },
    });

    if (!topic) {
      throw new NotFoundException(`Topic with ID ${id} not found`);
    }

    //find translation by topic id
    const translations = await this.translationService.getTranslationsByTable(
      id,
      TOPIC_TABLE_NAME,
    );
    const enTranslation = Object.fromEntries(
      translations
        .filter((t) => t.languageCode === LanguageCodeEnum.EN)
        .map((t) => [t.id, t.value, t.languageCode]),
    );

    const zhTranslation = Object.fromEntries(
      translations
        .filter((t) => t.languageCode === LanguageCodeEnum.ZHTW)
        .map((t) => [t.id, t.value, t.languageCode]),
    );

    let savedTopic = topic;
    if (enName && enTranslation && enName !== enTranslation.value) {
      topic.name = enName;
      topic.updaterUserId = userId;
      topic.updatedAt = BigInt(Date.now());

      savedTopic = await this.topicRepository.save(topic);

      await this.translationService.updateTranslationById(
        enTranslation.id,
        savedTopic.name,
        userId,
        TOPIC_TABLE_NAME,
        LanguageCodeEnum.EN,
        TOPIC_DATA_TRANSLATION_FIELD_NAME,
        savedTopic.id,
        uniqueEnValue,
      );
    }

    if (zhTwName && zhTranslation && zhTwName !== zhTranslation.value) {
      await this.translationService.updateTranslationById(
        zhTranslation.id,
        zhTwName,
        userId,
        TOPIC_TABLE_NAME,
        LanguageCodeEnum.ZHTW,
        TOPIC_DATA_TRANSLATION_FIELD_NAME,
        savedTopic.id,
        uniqueZhTwValue,
      );
    }

    return this.mapTopicToResponseFilterDto(savedTopic, zhTranslation?.value);
  }

  /**
   * Delete topic (soft delete)
   * @param id - Topic ID
   * @param userId - ID of the user deleting the topic
   * @throws NotFoundException if topic doesn't exist
   */
  async deleteTopic(id: number, userId: number): Promise<void> {
    const topic = await this.topicRepository.findOne({
      where: { id, deletedAt: null },
    });

    if (!topic) {
      throw new NotFoundException(`Topic with ID ${id} not found`);
    }

    // Soft delete the topic
    topic.deletedAt = BigInt(Date.now());
    topic.deleterUserId = userId;

    await this.topicRepository.save(topic);

    // Also soft delete all topic-page relationships
    await this.topicPageRepository.update(
      { topicId: id, deletedAt: null },
      {
        deletedAt: BigInt(Date.now()),
        deleterUserId: userId,
      },
    );
  }

  /**
   * Seed sample topic data
   * @param userId - ID of the user creating the seed data
   * @returns Array of created topics
   */
  async seedTopics(userId: number): Promise<TopicResponseAdminFilterPayload[]> {
    const sampleTopics: CreateTopicDto[] = [
      {
        name: {
          [LanguageCodeEnum.EN]: '3D Rendering',
          [LanguageCodeEnum.ZHTW]: '3D渲染',
        },
      },
      {
        name: {
          [LanguageCodeEnum.EN]: 'Product Design',
          [LanguageCodeEnum.ZHTW]: '产品设计',
        },
      },
      {
        name: {
          [LanguageCodeEnum.EN]: 'Visualization',
          [LanguageCodeEnum.ZHTW]: '可视化',
        },
      },
    ];

    const createdTopics: TopicResponseAdminFilterPayload[] = [];

    for (const topicData of sampleTopics) {
      try {
        const createdTopic = await this.createTopic(topicData, userId);
        createdTopics.push(createdTopic);
      } catch (error) {
        // Continue with next topic if one fails
        console.warn(
          `Failed to create topic "${topicData.name}":`,
          error.message,
        );
      }
    }

    return createdTopics;
  }

  /**
   * Map topic entity to response DTO
   * @param topic - Topic entity
   * @returns Topic response DTO
   */
  private mapTopicToResponseFilterDto(
    topic: Topic,
    zhTwName?: string,
  ): TopicResponseAdminFilterPayload {
    return {
      id: topic.id,
      enName: topic.name,
      zhTwName: zhTwName ?? undefined,
      createdAt: Number(topic.createdAt),
      updatedAt: Number(topic.updatedAt),
      deletedAt: topic.deletedAt ? Number(topic.deletedAt) : undefined,
      creator: mapUserToOperatorResponseDto(topic.creator),
      updater: mapUserToOperatorResponseDto(topic.updater),
      deleter: topic.deleter
        ? mapUserToOperatorResponseDto(topic.deleter)
        : undefined,
    };
  }

  async getPageIdsByTopicId(id: number): Promise<number[]> {
    const topic = await this.topicRepository.findOne({
      where: { id, deletedAt: null },
    });

    if (!topic) {
      throw new NotFoundException(`Topic with ID ${id} not found`);
    }

    const topicPages = await this.topicPageRepository.find({
      where: {
        topicId: id,
        deletedAt: null,
        page: {
          pageType: PageTypeEnum.CASE_STUDY_LIST,
          deletedAt: null,
          status: PageStatusEnum.PUBLISHED,
        },
      },
      take: 4,
    });

    // sort by topicPage order
    const sortedTopicPages = topicPages.sort((a, b) => a.order - b.order);
    const sortedCaseStudyIds = sortedTopicPages.map((tp) => tp.pageId);
    return sortedCaseStudyIds;
  }
}
