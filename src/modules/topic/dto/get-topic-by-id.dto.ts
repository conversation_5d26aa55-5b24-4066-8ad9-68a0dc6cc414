import { ApiProperty } from '@nestjs/swagger';
import { IsInt, Min } from 'class-validator';
import { TransformToNumber } from 'src/decorators/transformers/TransformToNumber';
import { customApiPropertyDescription } from 'src/utils/swagger';

export class GetTopicByIdDto {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'Topic ID',
      remarks: ['Must be a positive integer'],
    }),
    example: 1,
  })
  @IsInt()
  @Min(1)
  @TransformToNumber()
  readonly id: number;
}
