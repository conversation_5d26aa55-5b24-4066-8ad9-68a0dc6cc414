import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsArray, IsOptional, IsString } from 'class-validator';
import { PaginationDto } from 'src/shared/dto/pagination.dto';

export class GetTopicsDto extends PaginationDto {
  @Transform(({ value }) => {
    console.log('Transform value:', value);
    return Array.isArray(value) ? value : [value];
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ApiPropertyOptional({
    description: 'Search topic by keywords',
    example: ['rendering'],
    type: String,
    isArray: true,
  })
  readonly keywords?: string[];
}
