import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsObject, IsString, ValidateNested } from 'class-validator';
import { LanguageCodeEnum } from 'src/database/translation.entity';

export class CreateTopicNameFieldsWithLanguageCode {
  @ApiProperty({
    description: 'Topic name',
    type: () => String,
    example: '3D Rendering',
  })
  @IsString()
  [LanguageCodeEnum.EN]: string;

  @ApiProperty({
    description: 'CN Topic name',
    type: () => String,
    example: '3D渲染',
  })
  @IsString()
  [LanguageCodeEnum.ZHTW]: string;
}

export class CreateTopicDto {
  @ApiProperty({
    description: 'Translations',
    type: () => CreateTopicNameFieldsWithLanguageCode,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => CreateTopicNameFieldsWithLanguageCode)
  name: CreateTopic<PERSON>ameFieldsWithLanguageCode;
}
