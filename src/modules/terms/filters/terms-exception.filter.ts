import { HttpException, HttpStatus } from '@nestjs/common';
import { TermsFilter } from './terms.filter.interface';
import { CreateTermsOperationLogRes } from '../terms.interface';

export class TermsExceptionFilter implements TermsFilter<Error, HttpException> {
  getResponse(
    { message: originalErrorMessage }: Error,
    operationLogResult?: CreateTermsOperationLogRes,
  ): HttpException {
    let errorMessage: string;
    let statusCode: number;

    switch (originalErrorMessage) {
      default:
        errorMessage = originalErrorMessage;
        statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
        break;
    }

    return new HttpException(
      {
        message: errorMessage,
        operationLogResult,
      },
      statusCode,
    );
  }
}
