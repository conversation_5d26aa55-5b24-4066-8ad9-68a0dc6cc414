import { Injectable } from '@nestjs/common';
import {
  TermsFilter,
  GetTermsListResponseFilterPayload,
  TermsResponseFilterOriginalResult,
  TermsResponseFilterResult,
  GetTermsVersionListResponseFilterPayload,
  GetTermsVersionDetailResponseFilterPayload,
} from './terms.filter.interface';
import { TermsResponseFilterTypeEnum } from '../filters/terms.filter.interface';
import { GetTermsListResult } from '../entities/get-terms-list.entity';
import { GetTermsVersionListResult } from '../entities/get-terms-version-list.entity';
import { GetTermsVersionDetailResult } from '../entities/get-terms-version-detail.entity';

@Injectable()
export class TermsResponseFilter
  implements
    TermsFilter<TermsResponseFilterOriginalResult, TermsResponseFilterResult>
{
  getResponse(
    orig: TermsResponseFilterOriginalResult,
  ): TermsResponseFilterResult {
    switch (orig.type) {
      case TermsResponseFilterTypeEnum.GET_TERMS_LIST: {
        const payload = orig.payload as GetTermsListResponseFilterPayload;
        let result = new GetTermsListResult();
        result = {
          terms: payload.terms,
          totalCount: payload.totalCount,
          totalPage: Math.ceil(payload.totalCount / payload.size),
          currentPage: payload.page,
        };
        return result;
      }
      case TermsResponseFilterTypeEnum.GET_TERMS_VERSION_LIST: {
        const payload =
          orig.payload as GetTermsVersionListResponseFilterPayload;
        let result = new GetTermsVersionListResult();
        result = {
          versions: payload.versions,
          totalCount: payload.totalCount,
          totalPage: Math.ceil(payload.totalCount / payload.size),
          currentPage: payload.page,
        };
        return result;
      }
      case TermsResponseFilterTypeEnum.GET_TERMS_VERSION_DETAIL: {
        const payload =
          orig.payload as GetTermsVersionDetailResponseFilterPayload;
        let result = new GetTermsVersionDetailResult();
        result = { ...payload };
        return result;
      }
    }
  }
}
