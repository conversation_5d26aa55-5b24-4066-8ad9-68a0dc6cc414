import { GetTermsListResult } from '../entities/get-terms-list.entity';
import { GetTermsVersionListResult } from '../entities/get-terms-version-list.entity';
import { GetTermsVersionDetailResult } from '../entities/get-terms-version-detail.entity';

export enum TermsResponseFilterTypeEnum {
  GET_TERMS_LIST = 'GET_TERMS_LIST',
  GET_TERMS_VERSION_LIST = 'GET_TERMS_VERSION_LIST',
  GET_TERMS_VERSION_DETAIL = 'GET_TERMS_VERSION_DETAIL',
}

export interface TermsFilter<T, U> {
  getResponse(orig: T, operationLogResult?: any): U;
}

interface Creator {
  firstName: string;
  lastName: string;
  email: string;
}

export interface GetTermsListResponseFilterPayload {
  terms: {
    id: number;
    versionId: string;
    creator: Creator | null;
    createdAt: number;
    name: string;
    requiredRead: boolean;
  }[];
  totalCount: number;
  page: number;
  size: number;
}

export interface GetTermsVersionListResponseFilterPayload {
  versions: {
    id: string;
    creator: Creator | null;
    createdAt: number;
    name: string;
    requiredRead: boolean;
    termsId: number;
  }[];
  totalCount: number;
  page: number;
  size: number;
}

export interface GetTermsVersionDetailResponseFilterPayload {
  versionId: string;
  creator: Creator | null;
  content: string;
  createdAt: number;
  name: string;
  requiredRead: boolean;
  termsId: number;
  url: string;
}

export interface TermsResponseFilterOriginalResult {
  type: TermsResponseFilterTypeEnum;
  payload:
    | GetTermsListResponseFilterPayload
    | GetTermsVersionListResponseFilterPayload
    | GetTermsVersionDetailResponseFilterPayload;
}

export type TermsResponseFilterResult =
  | GetTermsListResult
  | GetTermsVersionListResult
  | GetTermsVersionDetailResult;
