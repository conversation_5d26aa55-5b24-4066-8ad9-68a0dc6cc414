import { Injectable } from '@nestjs/common';
import {
  GetTermsListReq,
  CreateTermsVersionReq,
  GetTermsVersionListReq,
  GetTermsVersionDetailReq,
  CreateTermsOperationLogReq,
  OperationTypeOfTermsEnum,
  GetTermsListRes,
  GetTermsVersionListRes,
  GetTermsVersionDetailRes,
  CreateTermsOperationLogRes,
} from './terms.interface';
import { OperatorTermsServiceByBackend } from '../../shared/restful/backend/operator/terms/terms.service';
import { UsersService } from '../users/users.service';
import { OperationLogsService } from '../operation-logs/operation-logs.service';
import {
  CreateFailedOperationLogReq,
  CreateSuccessfulOperationLogReq,
} from '../operation-logs/operation-logs.interface';
import { OperationConfig } from '../operation-object-groups/operation-object-groups.interface';
import { CasbinService } from '../casbin/casbin.service';
import { PermissionConfig } from '../permission-object-groups/permission-object-groups.interface';

@Injectable()
export class TermsService {
  constructor(
    private readonly casbinService: CasbinService,
    private readonly operatorOperatorTermsServiceByBackend: OperatorTermsServiceByBackend,
    private readonly userService: UsersService,
    private readonly operationLogsService: OperationLogsService,
  ) {}
  public async createTermsOperationLog({
    type,
    isSuccess,
    operatorUserId,
    termsId,
    termsName,
    inputData,
    outputData,
    remarks,
    error,
  }: CreateTermsOperationLogReq): Promise<CreateTermsOperationLogRes> {
    const successfulOrFailedOperationLogs:
      | CreateSuccessfulOperationLogReq[]
      | CreateFailedOperationLogReq[] = [];
    const failedOperationLogs: CreateFailedOperationLogReq[] = [];

    const targetId = termsId?.toString();
    const targetName = termsName;
    const creatorUserId = operatorUserId;

    switch (type) {
      case OperationTypeOfTermsEnum.GET_TERMS_LIST: {
        const {
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
        } = OperationConfig.TERMS_MANAGEMENT.TERMS_LIST.GET_TERMS_LIST;
        successfulOrFailedOperationLogs.push({
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
          inputData,
          outputData,
          creatorUserId,
        });
        break;
      }
      case OperationTypeOfTermsEnum.CREATE_TERMS_VERSION: {
        const {
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
        } = OperationConfig.TERMS_MANAGEMENT.TERMS_LIST.CREATE_TERMS_VERSION;
        successfulOrFailedOperationLogs.push({
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
          targetId,
          targetName,
          inputData,
          creatorUserId,
        });
        break;
      }
      case OperationTypeOfTermsEnum.GET_TERMS_VERSION_LIST: {
        const {
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
        } = OperationConfig.TERMS_MANAGEMENT.TERMS_LIST.GET_TERMS_VERSION_LIST;
        successfulOrFailedOperationLogs.push({
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
          targetId,
          targetName,
          inputData,
          outputData,
          creatorUserId,
        });
        break;
      }
      case OperationTypeOfTermsEnum.GET_TERMS_VERSION_DETAIL: {
        const {
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
        } =
          OperationConfig.TERMS_MANAGEMENT.TERMS_LIST.GET_TERMS_VERSION_DETAIL;
        successfulOrFailedOperationLogs.push({
          operationObjectGroupKey,
          operationObjectKey,
          operationKey,
          targetType,
          targetId,
          targetName,
          inputData,
          outputData,
          remarks,
          creatorUserId,
        });
        break;
      }
      default:
        break;
    }
    if (isSuccess) {
      await this.operationLogsService.createSuccessfulOperationLogs(
        successfulOrFailedOperationLogs,
      );
    }

    successfulOrFailedOperationLogs.forEach(
      (successfulOrFailedOperationLog) => {
        failedOperationLogs.push({
          ...successfulOrFailedOperationLog,
          failCode: error?.code || null,
          failReason: error?.message || error,
        });
      },
    );
    return this.operationLogsService.createFailedOperationLogs(
      failedOperationLogs,
    );
  }

  /**
   * @description 檢查是否有檢視的權限
   */
  public async checkTermsListReadPermission(
    operatorUserId: number,
  ): Promise<void> {
    await this.casbinService.checkPermission(
      operatorUserId,
      PermissionConfig.TERMS_MANAGEMENT.TERMS_LIST.READ,
    );
  }

  /**
   * @description 檢查是否有新增版本的權限
   */
  public async checkTermsLisCreateVersionPermission(
    operatorUserId: number,
  ): Promise<void> {
    await this.casbinService.checkPermission(
      operatorUserId,
      PermissionConfig.TERMS_MANAGEMENT.TERMS_LIST.CREATE_VERSION,
    );
  }

  public async getTermsList({
    size,
    page,
  }: GetTermsListReq): Promise<GetTermsListRes> {
    const { termsList, totalCount } =
      await this.operatorOperatorTermsServiceByBackend.getTermsList({
        size,
        page,
      });

    const operatorMap = await this.userService.getUserMapByUserIds({
      userIds: termsList.map(({ operatorId }) => operatorId),
    });

    return {
      terms: termsList.map((terms) => ({
        ...terms,
        creator: operatorMap.get(terms.operatorId) || null,
      })),
      totalCount,
    };
  }

  public async createTermsVersion({
    operatorUserId,
    termsId,
    content,
    requiredRead,
    url,
  }: CreateTermsVersionReq): Promise<void> {
    await this.operatorOperatorTermsServiceByBackend.createTermsVersion({
      operatorUserId,
      termsId,
      content,
      requiredRead,
      url,
    });
  }

  public async getTermsVersionList({
    termsId,
    size,
    page,
  }: GetTermsVersionListReq): Promise<GetTermsVersionListRes> {
    const { versions, totalCount } =
      await this.operatorOperatorTermsServiceByBackend.getTermsVersionList({
        termsId,
        size,
        page,
      });
    const operatorMap = await this.userService.getUserMapByUserIds({
      userIds: versions.map(({ operatorId }) => operatorId),
    });
    return {
      versions: versions.map((version) => ({
        ...version,
        creator: operatorMap.get(version.operatorId) || null,
      })),
      totalCount,
    };
  }

  public async getTermsVersionDetail({
    termsId,
    versionId,
  }: GetTermsVersionDetailReq): Promise<GetTermsVersionDetailRes> {
    const terms =
      await this.operatorOperatorTermsServiceByBackend.getTermsVersionDetail({
        termsId,
        versionId,
      });

    const operatorMap = await this.userService.getUserMapByUserIds({
      userIds: [terms.operatorId],
    });
    const operator = operatorMap.get(terms.operatorId);

    return {
      ...terms,
      creator: operator || null,
    };
  }

  // TODO: 傳入 id 取得 terms 而非寫死 size, page 去查詢
  public async getTermsNameNameByTermsId(termsId: number): Promise<string> {
    const { termsList } =
      await this.operatorOperatorTermsServiceByBackend.getTermsList({
        size: 1000,
        page: 1,
      });
    const { name } = termsList.find(({ id }) => id === termsId);
    return name || '';
  }
}
