import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsBoolean, IsOptional, IsUrl } from 'class-validator';
import { customApiPropertyDescription } from '../../../utils/swagger';
import { IsNotBlank } from '../../../decorators/validations/IsNotBlank';

export class CreateTermsVersionDto {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '條款內容',
    }),
    example: '<h1>title</h1>',
  })
  @IsString()
  @IsNotBlank()
  readonly content: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '此版本是否為用戶必讀條款',
    }),
    example: true,
  })
  @IsBoolean()
  readonly requiredRead: boolean;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '條款url',
      remarks: ['目前不開放前端頁面修改url'],
    }),
    type: String,
    required: false,
    example: 'https://xxxxx.xxxx',
  })
  @IsOptional()
  @IsUrl()
  readonly url?: string;
}
