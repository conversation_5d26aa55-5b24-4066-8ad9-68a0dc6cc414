import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, Min } from 'class-validator';
import { TransformToNumber } from '../../../decorators/transformers/TransformToNumber';
import { customApiPropertyDescription } from '../../../utils/swagger';

export class GetTermsVersionListDto {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '每頁筆數',
      remarks: ['需大於等於 1'],
      defaultValue: '30',
    }),
    required: false,
    example: 30,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @TransformToNumber()
  readonly size: number = 30;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '頁數',
      remarks: ['需大於等於 1'],
      defaultValue: '1',
    }),
    required: false,
    example: 1,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @TransformToNumber()
  readonly page: number = 1;
}
