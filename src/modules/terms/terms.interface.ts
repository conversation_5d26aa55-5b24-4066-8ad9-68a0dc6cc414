import { CreateOperationLogRes } from '../operation-logs/operation-logs.interface';

export enum OperationTypeOfTermsEnum {
  GET_TERMS_LIST = 'GET_TERMS_LIST',
  GET_TERMS_VERSION_LIST = 'GET_TERMS_VERSION_LIST',
  CREATE_TERMS_VERSION = 'CREATE_TERMS_VERSION',
  GET_TERMS_VERSION_DETAIL = 'GET_TERMS_VERSION_DETAIL',
}

export interface CreateTermsOperationLogReq {
  type: string;
  isSuccess: boolean;
  operatorUserId: number;
  termsId?: number;
  termsName?: string;
  inputData?: Record<string, any>;
  outputData?: Record<string, any>;
  remarks?: string[];
  error?: any;
}
export interface CreateTermsOperationLogRes extends CreateOperationLogRes {}

interface Creator {
  firstName: string;
  lastName: string;
  email: string;
}

export interface GetTermsListReq {
  size: number;
  page: number;
}
export interface GetTermsListRes {
  terms: {
    id: number;
    versionId: string;
    creator: Creator | null;
    createdAt: number;
    name: string;
    requiredRead: boolean;
  }[];
  totalCount: number;
}

export interface GetTermsVersionListReq {
  termsId: number;
  size: number;
  page: number;
}
export interface GetTermsVersionListRes {
  versions: {
    id: string;
    creator: Creator | null;
    createdAt: number;
    name: string;
    requiredRead: boolean;
    termsId: number;
  }[];
  totalCount: number;
}

export interface GetTermsVersionDetailReq {
  termsId: number;
  versionId: string;
}
export interface GetTermsVersionDetailRes {
  versionId: string;
  creator: Creator | null;
  content: string;
  createdAt: number;
  name: string;
  requiredRead: boolean;
  termsId: number;
  url: string;
}

export interface CreateTermsVersionReq {
  operatorUserId: number;
  termsId: number;
  content: string;
  requiredRead: boolean;
  url?: string;
}
