import {
  Controller,
  Get,
  UseGuards,
  Req,
  Query,
  Param,
  Post,
  Body,
  ParseIntPipe,
} from '@nestjs/common';
import UserAuthGuard from '../auth/guards/user-auth.guard';
import {
  ApiBearerAuth,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiCreatedResponse,
  ApiBody,
} from '@nestjs/swagger';
import { TermsService } from './terms.service';
import {
  GetTermsListResult,
  GetTermsListRo,
} from './entities/get-terms-list.entity';
import { GetTermsListDto } from './dto/get-terms-list.dto';
import { GetTermsVersionListDto } from './dto/get-terms-version-list.dto';
import { CreateTermsVersionDto } from './dto/create-terms-version.dto';
import { TermsResponseFilter } from './filters/terms-response.filter';
import { TermsExceptionFilter } from './filters/terms-exception.filter';
import { TermsResponseFilterTypeEnum } from './filters/terms.filter.interface';
import { CreateTermsVersionRo } from './entities/create-terms-version.entity';
import {
  GetTermsVersionListResult,
  GetTermsVersionListRo,
} from './entities/get-terms-version-list.entity';
import {
  GetTermsVersionDetailRo,
  GetTermsVersionDetailResult,
} from './entities/get-terms-version-detail.entity';
import {
  CreateTermsOperationLogReq,
  OperationTypeOfTermsEnum,
} from './terms.interface';
import { ApiResultAndCreateOperationLogResult } from '../../interceptors/response/response.interface';
import { customApiOperationDescription } from '../../utils/swagger';

@ApiBearerAuth()
@UseGuards(UserAuthGuard)
@Controller('terms')
export class TermsController {
  constructor(
    private readonly termsService: TermsService,
    private readonly termsResponseFilter: TermsResponseFilter,
    private readonly termsExceptionFilter: TermsExceptionFilter,
  ) {}

  @ApiTags('條款管理 > 條款列表')
  @Get()
  @ApiOperation({
    summary: '取得條款列表',
    description: customApiOperationDescription({
      permissions: ['條款管理 > 條款列表 > 檢視'],
      operationLogs: ['條款管理 > 條款列表 > 查詢條款清單'],
      relatedBEApis: ['[GET] /operator/v1/terms'],
    }),
  })
  @ApiOkResponse({ type: GetTermsListRo })
  async getTermsList(
    @Req() req,
    @Query() getTermsListDto: GetTermsListDto,
  ): Promise<ApiResultAndCreateOperationLogResult<GetTermsListResult>> {
    const { userId: operatorUserId } = req.user;
    const createTermsOperationLogReq: CreateTermsOperationLogReq = {
      type: OperationTypeOfTermsEnum.GET_TERMS_LIST,
      isSuccess: true,
      operatorUserId,
      inputData: getTermsListDto,
    };
    try {
      await this.termsService.checkTermsListReadPermission(operatorUserId);
      const res = await this.termsService.getTermsList(getTermsListDto);
      const result = this.termsResponseFilter.getResponse({
        type: TermsResponseFilterTypeEnum.GET_TERMS_LIST,
        payload: {
          ...res,
          page: getTermsListDto.page,
          size: getTermsListDto.size,
        },
      }) as GetTermsListResult;
      const operationLogResult =
        await this.termsService.createTermsOperationLog({
          ...createTermsOperationLogReq,
          outputData: result,
        });
      return { result, operationLogResult };
    } catch (error) {
      const operationLogResult =
        await this.termsService.createTermsOperationLog({
          ...createTermsOperationLogReq,
          isSuccess: false,
          error,
        });
      throw this.termsExceptionFilter.getResponse(error, operationLogResult);
    }
  }

  @ApiTags('條款管理 > 條款列表')
  @Post(':termsId')
  @ApiOperation({
    summary: '新增條款版本',
    description: customApiOperationDescription({
      permissions: ['條款管理 > 條款列表 > 新增版本'],
      operationLogs: ['條款管理 > 條款列表 > 新增條款版本'],
      relatedBEApis: ['[POST] /operator/v1/terms/{termID}'],
    }),
  })
  @ApiBody({ type: CreateTermsVersionDto })
  @ApiCreatedResponse({ type: CreateTermsVersionRo })
  async createTermsVersion(
    @Req() req,
    @Param('termsId', ParseIntPipe) termsId: number,
    @Body() createTermsVersionDto: CreateTermsVersionDto,
  ): Promise<ApiResultAndCreateOperationLogResult<void>> {
    const { userId: operatorUserId } = req.user;
    const createTermsOperationLogReq: CreateTermsOperationLogReq = {
      type: OperationTypeOfTermsEnum.CREATE_TERMS_VERSION,
      isSuccess: true,
      operatorUserId,
      termsId: termsId,
      termsName: await this.termsService.getTermsNameNameByTermsId(termsId),
      inputData: createTermsVersionDto,
    };
    try {
      await this.termsService.checkTermsLisCreateVersionPermission(
        operatorUserId,
      );
      await this.termsService.createTermsVersion({
        termsId,
        operatorUserId,
        ...createTermsVersionDto,
      });
      const operationLogResult =
        await this.termsService.createTermsOperationLog({
          ...createTermsOperationLogReq,
        });
      return { operationLogResult };
    } catch (error) {
      const operationLogResult =
        await this.termsService.createTermsOperationLog({
          ...createTermsOperationLogReq,
          isSuccess: false,
          error,
        });
      throw this.termsExceptionFilter.getResponse(error, operationLogResult);
    }
  }

  @ApiTags('條款管理 > 條款列表')
  @Get(':termsId/versions')
  @ApiOperation({
    summary: '取得條款版本列表',
    description: customApiOperationDescription({
      permissions: ['條款管理 > 條款列表 > 檢視'],
      operationLogs: ['條款管理 > 條款列表 > 查詢條款版本清單'],
      relatedBEApis: ['[GET] /operator/v1/terms/{termID}/versions'],
    }),
  })
  @ApiOkResponse({ type: GetTermsVersionListRo })
  async getTermsVersionList(
    @Req() req,
    @Param('termsId', ParseIntPipe) termsId: number,
    @Query() getTermsVersionListDto: GetTermsVersionListDto,
  ): Promise<ApiResultAndCreateOperationLogResult<GetTermsVersionListResult>> {
    const { userId: operatorUserId } = req.user;
    const createTermsOperationLogReq: CreateTermsOperationLogReq = {
      type: OperationTypeOfTermsEnum.GET_TERMS_VERSION_LIST,
      isSuccess: true,
      operatorUserId,
      termsId,
      termsName: await this.termsService.getTermsNameNameByTermsId(termsId),
      inputData: getTermsVersionListDto,
    };
    try {
      await this.termsService.checkTermsListReadPermission(operatorUserId);
      const res = await this.termsService.getTermsVersionList({
        termsId,
        ...getTermsVersionListDto,
      });
      const result = this.termsResponseFilter.getResponse({
        type: TermsResponseFilterTypeEnum.GET_TERMS_VERSION_LIST,
        payload: {
          ...res,
          page: getTermsVersionListDto.page,
          size: getTermsVersionListDto.size,
        },
      }) as GetTermsVersionListResult;
      const operationLogResult =
        await this.termsService.createTermsOperationLog({
          ...createTermsOperationLogReq,
          outputData: result,
        });
      return { result, operationLogResult };
    } catch (error) {
      const operationLogResult =
        await this.termsService.createTermsOperationLog({
          ...createTermsOperationLogReq,
          isSuccess: false,
          error,
        });
      throw this.termsExceptionFilter.getResponse(error, operationLogResult);
    }
  }

  @ApiTags('條款管理 > 條款列表')
  @Get(':termsId/versions/:versionId')
  @ApiOperation({
    summary: '取得條款版本的詳細資料',
    description: customApiOperationDescription({
      permissions: ['條款管理 > 條款列表 > 檢視'],
      operationLogs: ['條款管理 > 條款列表 > 查詢條款版本細節'],
      relatedBEApis: ['[GET] /operator/v1/terms/{termID}/version/{versionId}'],
    }),
  })
  @ApiOkResponse({ type: GetTermsVersionDetailRo })
  async getTermsVersionDetail(
    @Req() req,
    @Param('termsId', ParseIntPipe) termsId: number,
    @Param('versionId') versionId: string,
  ): Promise<
    ApiResultAndCreateOperationLogResult<GetTermsVersionDetailResult>
  > {
    const { userId: operatorUserId } = req.user;
    const createTermsOperationLogReq: CreateTermsOperationLogReq = {
      type: OperationTypeOfTermsEnum.GET_TERMS_VERSION_DETAIL,
      isSuccess: true,
      operatorUserId,
      termsId: termsId,
      termsName: await this.termsService.getTermsNameNameByTermsId(termsId),
      remarks: [`版本 ID: ${versionId}`],
    };
    try {
      await this.termsService.checkTermsListReadPermission(operatorUserId);
      const res = await this.termsService.getTermsVersionDetail({
        termsId,
        versionId,
      });
      const result = this.termsResponseFilter.getResponse({
        type: TermsResponseFilterTypeEnum.GET_TERMS_VERSION_DETAIL,
        payload: res,
      }) as GetTermsVersionDetailResult;
      const operationLogResult =
        await this.termsService.createTermsOperationLog({
          ...createTermsOperationLogReq,
          outputData: result,
        });
      return { result, operationLogResult };
    } catch (error) {
      const operationLogResult =
        await this.termsService.createTermsOperationLog({
          ...createTermsOperationLogReq,
          isSuccess: false,
          error,
        });
      throw this.termsExceptionFilter.getResponse(error, operationLogResult);
    }
  }
}
