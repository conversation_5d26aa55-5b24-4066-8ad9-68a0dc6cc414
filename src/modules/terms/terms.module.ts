import { Modu<PERSON> } from '@nestjs/common';
import { TermsService } from './terms.service';
import { TermsController } from './terms.controller';
import { TermsResponseFilter } from './filters/terms-response.filter';
import { TermsExceptionFilter } from './filters/terms-exception.filter';
import { OperatorTermsModuleByBackend } from '../../shared/restful/backend/operator/terms/terms.module';
import { UsersModule } from '../users/users.module';
import { OperationLogsModule } from '../operation-logs/operation-logs.module';
import { CasbinModule } from '../casbin/casbin.module';

@Module({
  imports: [
    OperatorTermsModuleByBackend,
    UsersModule,
    CasbinModule,
    OperationLogsModule,
  ],
  controllers: [TermsController],
  providers: [TermsService, TermsResponseFilter, TermsExceptionFilter],
})
export class TermsModule {}
