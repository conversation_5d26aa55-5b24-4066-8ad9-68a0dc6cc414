import { ApiProperty } from '@nestjs/swagger';
import {
  ApiResponseStatusEnum,
  ResponseInterface,
  LogResponseStatusEnum,
} from '../../../interceptors/response/response.interface';
import { customApiPropertyDescription } from '../../../utils/swagger';

class TermsVersionDetailCreator {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '名字',
    }),
    example: '名字',
  })
  firstName: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '姓氏',
    }),
    example: '姓氏',
  })
  lastName: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '信箱',
    }),
    example: '<EMAIL>',
  })
  email: string;
}

export class GetTermsVersionDetailResult {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '版本 ID',
    }),
    example: '1',
  })
  versionId: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '建立者',
    }),
  })
  creator: TermsVersionDetailCreator | null;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '內容',
    }),
    example: '<h1>title</h1>',
  })
  content: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '建立時間',
    }),
    example: 162250560000,
  })
  createdAt: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '條款名稱',
    }),
    example: 'hidol會員服務條款',
  })
  name: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '此版本是否為用戶必讀條款',
    }),
    example: true,
  })
  requiredRead: boolean;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '條款 ID',
    }),
    example: 1,
  })
  termsId: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '條款連結',
    }),
    example: 'https://xxxx.xxx.xx',
  })
  url: string;
}

export class GetTermsVersionDetailRo
  implements ResponseInterface<GetTermsVersionDetailResult>
{
  @ApiProperty({ example: 200 })
  code: number;

  @ApiProperty({
    enum: LogResponseStatusEnum,
    example: LogResponseStatusEnum.SUCCESS,
  })
  logStatus: LogResponseStatusEnum;

  @ApiProperty({
    enum: ApiResponseStatusEnum,
    example: ApiResponseStatusEnum.SUCCESS,
  })
  status: ApiResponseStatusEnum;

  @ApiProperty({ type: GetTermsVersionDetailResult })
  data: GetTermsVersionDetailResult;
}
