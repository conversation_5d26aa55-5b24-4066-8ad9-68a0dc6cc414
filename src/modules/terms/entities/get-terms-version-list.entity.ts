import { ApiProperty } from '@nestjs/swagger';
import {
  ApiResponseStatusEnum,
  LogResponseStatusEnum,
  ResponseInterface,
} from '../../../interceptors/response/response.interface';
import { customApiPropertyDescription } from '../../../utils/swagger';

class TermsVersionCreator {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '名字',
    }),
    example: '名字',
  })
  firstName: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '姓氏',
    }),
    example: '姓氏',
  })
  lastName: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '信箱',
    }),
    example: '<EMAIL>',
  })
  email: string;
}

export class TermsVersion {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '版本 ID',
    }),
    example: '1',
  })
  id: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '建立者',
    }),
  })
  creator: TermsVersionCreator | null;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '建立時間',
    }),
    example: 162250560000,
  })
  createdAt: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '條款名稱',
    }),
    example: 'hidol會員服務條款',
  })
  name: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '此版本是否為用戶必讀條款',
    }),
    example: true,
  })
  requiredRead: boolean;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '條款 ID',
    }),
    example: 1,
  })
  termsId: number;
}
export class GetTermsVersionListResult {
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '版本列表',
    }),
    type: [TermsVersion],
  })
  versions: TermsVersion[];

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '總數量',
    }),
    example: 1,
  })
  totalCount: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '總頁數',
    }),
    example: 30,
  })
  totalPage: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: '當前頁數',
    }),
    example: 1,
  })
  currentPage: number;
}

export class GetTermsVersionListRo
  implements ResponseInterface<GetTermsVersionListResult>
{
  @ApiProperty({ example: 200 })
  code: number;

  @ApiProperty({
    enum: LogResponseStatusEnum,
    example: LogResponseStatusEnum.SUCCESS,
  })
  logStatus: LogResponseStatusEnum;

  @ApiProperty({
    enum: ApiResponseStatusEnum,
    example: ApiResponseStatusEnum.SUCCESS,
  })
  status: ApiResponseStatusEnum;

  @ApiProperty({ type: GetTermsVersionListResult })
  data: GetTermsVersionListResult;
}
