import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { LanguageCodeEnum, Translation } from 'src/database/translation.entity';
import { In, Repository } from 'typeorm';

@Injectable()
export class TranslationService {
  constructor(
    @InjectRepository(Translation)
    private readonly translationRepository: Repository<Translation>,
  ) {}

  async createTranslationById(
    resourceId: number,
    resourceTableName: string,
    operationUserId: number,
    value: string,
    resourceFieldName: string,
    languageCode: LanguageCodeEnum = LanguageCodeEnum.EN,
    uniqueValue?: string,
  ): Promise<void> {
    const translation = this.translationRepository.create({
      resourceTableName: resourceTableName,
      languageCode,
      resourceFieldName: resourceFieldName,
      value,
      creatorUserId: operationUserId,
      updaterUserId: operationUserId,
      resourceId: resourceId,
      uniqueValue,
    });
    await this.translationRepository.save(translation);
  }

  async updateTranslationById(
    id: number,
    value: string,
    operationUserId: number,
    resourceTableName: string,
    languageCode: LanguageCodeEnum = LanguageCodeEnum.EN,
    resourceFieldName: string,
    resourceId: number,
    uniqueValue?: string,
  ): Promise<void> {
    await this.translationRepository.update(id, {
      resourceTableName: resourceTableName,
      languageCode,
      resourceFieldName: resourceFieldName,
      value: value as any,
      updaterUserId: operationUserId,
      creatorUserId: operationUserId,
      resourceId: resourceId,
      updatedAt: BigInt(Date.now()),
      uniqueValue,
    });
  }

  async batchCreateTranslation(
    resourceId: number,
    resourceTableName: string,
    operationUserId: number,
    values: {
      resourceFieldName: string;
      value: string;
      languageCode: LanguageCodeEnum;
    }[],
  ): Promise<void> {
    const data = values.map((v) =>
      this.translationRepository.create({
        resourceTableName: resourceTableName,
        languageCode: v.languageCode,
        resourceFieldName: v.resourceFieldName,
        value: v.value,
        creatorUserId: operationUserId,
        updaterUserId: operationUserId,
        resourceId: resourceId,
      }),
    );
    await this.translationRepository.insert(data);
  }

  async getTranslationsByTable(
    resourceId: number | number[],
    resourceTableName: string,
    languageCode?: LanguageCodeEnum,
  ): Promise<Record<string, any>> {
    const whereCondition = Array.isArray(resourceId)
      ? { resourceId: In(resourceId), resourceTableName, deletedAt: null }
      : { resourceId, resourceTableName, deletedAt: null };

    if (languageCode) {
      whereCondition['languageCode'] = languageCode;
    }
    const translations = await this.translationRepository.find({
      where: whereCondition,
    });

    const result: Record<string, any> = {};
    for (const t of translations) {
      if (!result[t.resourceId]) {
        result[t.resourceId] = {};
      }
      result[t.resourceId][t.resourceFieldName] = t.value;
    }
    return result;
  }

  async getTranslationsByTableAndField(
    resourceId: number | number[],
    resourceTableName: string,
    resourceFieldName: string,
    languageCode?: LanguageCodeEnum,
  ): Promise<Translation[]> {
    const whereCondition = Array.isArray(resourceId)
      ? { resourceId: In(resourceId), resourceTableName, deletedAt: null }
      : { resourceId, resourceTableName, deletedAt: null };

    if (languageCode) {
      whereCondition['languageCode'] = languageCode;
    }
    const translations = await this.translationRepository.find({
      where: { ...whereCondition, resourceFieldName },
    });
    return translations;
  }

  async getTranslationsByValue(
    value: string[],
    resourceFieldName: string,
    resourceTableName: string,
  ): Promise<Translation[]> {
    try {
      const translations = await this.translationRepository.find({
        where: {
          value: In(value),
          resourceFieldName,
          resourceTableName,
          deletedAt: null,
        },
      });
      return translations;
    } catch (error) {
      throw error;
    }
  }

  async getTranslationsByUniqueValue(
    uniqueValue: string | string[],
    resourceFieldName: string,
    resourceTableName: string,
  ): Promise<Translation[]> {
    try {
      const condition = Array.isArray(uniqueValue)
        ? { uniqueValue: In(uniqueValue) }
        : { uniqueValue };
      const translations = await this.translationRepository.find({
        where: {
          ...condition,
          resourceTableName,
          resourceFieldName,
          deletedAt: null,
        },
      });
      return translations;
    } catch (error) {
      throw error;
    }
  }

  async deleteTranslationBy(
    resourceId: number | number[],
    resourceTableName: string,
    operationUserId: number,
  ): Promise<void> {
    const condition = Array.isArray(resourceId)
      ? { resourceId: In(resourceId) }
      : { resourceId: resourceId };
    await this.translationRepository.update(
      {
        ...condition,
        resourceTableName: resourceTableName,
      },
      {
        deletedAt: BigInt(Date.now()),
        deleterUserId: operationUserId,
      },
    );
  }
}
