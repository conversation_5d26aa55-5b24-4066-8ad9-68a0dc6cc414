import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Translation } from 'src/database/translation.entity';
import { TranslationService } from 'src/modules/translation/services/translation.services';

@Module({
  imports: [TypeOrmModule.forFeature([Translation])],
  providers: [TranslationService],
  exports: [TranslationService],
})
export class TranslationModule {}
