import { ApiProperty } from '@nestjs/swagger';
import { IsInt, Min } from 'class-validator';

export class PaginationResult<T> {
  @IsInt()
  @Min(1)
  @ApiProperty({
    description: 'Total count',
    example: 1,
  })
  totalCount: number;

  @IsInt()
  @Min(1)
  @ApiProperty({
    description: 'Total page',
    example: 1,
  })
  totalPage: number;

  @IsInt()
  @Min(1)
  @ApiProperty({
    description: 'Current page',
    example: 1,
  })
  currentPage: number;

  @IsInt()
  @Min(1)
  @ApiProperty({
    description: 'Items per page',
    example: 10,
  })
  size: number;

  @ApiProperty({
    description: 'Data',
  })
  data: T[];
}
