import { ApiProperty } from '@nestjs/swagger';
import { IsInt, Min } from 'class-validator';
import { customApiPropertyDescription } from 'src/utils/swagger';

export class OperatorResult {
  @IsInt()
  @Min(1)
  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'Operator ID',
    }),
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'Operator email',
    }),
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'Operator first name',
    }),
    example: '名字',
  })
  firstName: string;

  @ApiProperty({
    description: customApiPropertyDescription({
      basic: 'Operator last name',
    }),
    example: '姓氏',
  })
  lastName: string;
}
