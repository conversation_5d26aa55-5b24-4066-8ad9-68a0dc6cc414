import { AxiosRequestConfig } from 'axios';

export enum ApiProviderEnum {
  BACKEND = 'BACKEND',
}

export enum ApiErrorMessagePrefixEnum {
  BACKEND = '[Backend]',
}

export enum ApiMethodEnum {
  POST = 'POST',
  GET = 'GET',
  PUT = 'PUT',
  PATCH = 'PATCH',
  DELETE = 'DELETE',
}

export interface ApiRequest {
  provider?: ApiProviderEnum;
  method: ApiMethodEnum;
  url: string;
  data?: any;
  config?: AxiosRequestConfig;
}

export interface ApiErrorByBackend {
  code: number;
  message: string;
}
