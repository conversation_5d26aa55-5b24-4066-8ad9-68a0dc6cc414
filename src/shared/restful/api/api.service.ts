import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { AxiosError } from 'axios';
import {
  ApiErrorByBackend,
  ApiErrorMessagePrefixEnum,
  ApiMethodEnum,
  ApiProviderEnum,
  ApiRequest,
} from './api.interface';
import { Observable, catchError, map } from 'rxjs';

@Injectable()
export class ApiService {
  constructor(private httpService: HttpService) {}

  getErrorMessage(
    provider: ApiProviderEnum,
    error: AxiosError,
  ): string | AxiosError {
    switch (provider) {
      case ApiProviderEnum.BACKEND: {
        const { code, message } =
          (error?.response?.data as ApiErrorByBackend) || {};
        return code
          ? `${ApiErrorMessagePrefixEnum.BACKEND} ${
              message || ''
            } (code: ${code})`
          : `${ApiErrorMessagePrefixEnum.BACKEND} 非預期的錯誤`;
      }
      default:
        return error;
    }
  }

  request<T>({
    provider,
    method,
    url,
    data,
    config,
  }: ApiRequest): Observable<T> {
    switch (method) {
      case ApiMethodEnum.GET: {
        return this.httpService.get(url, config).pipe(
          map((response) => response?.data as T),
          catchError((error: AxiosError) => {
            throw new Error(this.getErrorMessage(provider, error) as any);
          }),
        );
      }
      case ApiMethodEnum.POST: {
        return this.httpService.post(url, data, config).pipe(
          map((response) => response?.data as T),
          catchError((error: AxiosError) => {
            throw new Error(this.getErrorMessage(provider, error) as any);
          }),
        );
      }
      case ApiMethodEnum.PATCH: {
        return this.httpService.patch(url, data, config).pipe(
          map((response) => response?.data as T),
          catchError((error: AxiosError) => {
            throw new Error(this.getErrorMessage(provider, error) as any);
          }),
        );
      }
      case ApiMethodEnum.PUT: {
        return this.httpService.put(url, data, config).pipe(
          map((response) => response?.data as T),
          catchError((error: AxiosError) => {
            throw new Error(this.getErrorMessage(provider, error) as any);
          }),
        );
      }
      case ApiMethodEnum.DELETE: {
        return this.httpService.delete(url, config).pipe(
          map((response) => response?.data as T),
          catchError((error: AxiosError) => {
            throw new Error(this.getErrorMessage(provider, error) as any);
          }),
        );
      }
      default:
    }
  }
}
