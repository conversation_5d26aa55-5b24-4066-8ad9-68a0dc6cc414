import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  GetUserListByOpenIdsByBackendReq,
  GetUserListByOpenIdsByBackendRes,
  GetUserListByOpenIdsByBackendApiRes,
} from './users.interface';
import { ApiService } from '../../../api/api.service';
import { ApiMethodEnum, ApiProviderEnum } from '../../../api/api.interface';
import { Observable, lastValueFrom } from 'rxjs';

@Injectable()
export class OperatorUsersServiceByBackend {
  private readonly url: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly apiService: ApiService,
  ) {
    this.url = `${this.configService.get(
      'shared.restful.backend.url',
    )}/operator/v1/users`;
  }

  /**
   * @description [GET] /operator/v1/users
   */
  async getUserListByOpenIds({
    openIds,
  }: GetUserListByOpenIdsByBackendReq): Promise<GetUserListByOpenIdsByBackendRes> {
    const apiRes = await lastValueFrom(
      this.apiService.request({
        provider: ApiProviderEnum.BACKEND,
        method: ApiMethodEnum.GET,
        url: this.url,
        config: { params: { open_ids: openIds.join(',') } },
      }) as Observable<GetUserListByOpenIdsByBackendApiRes>,
    );
    return {
      users: apiRes.users.map((user) => ({ ...user, openId: user.openID })),
    };
  }
}
