import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ApiService } from '../../../api/api.service';
import { ApiMethodEnum, ApiProviderEnum } from '../../../api/api.interface';
import { Observable, lastValueFrom } from 'rxjs';
import {
  GetTermsListByBackendReq,
  GetTermsListByBackendRes,
  GetTermsListByBackendApiRes,
  CreateTermsVersionByBackendRes,
  GetTermsVersionListByBackendReq,
  GetTermsVersionListByBackendApiRes,
  GetTermsVersionListByBackendRes,
  GetTermsVersionDetailByBackendReq,
  GetTermsVersionDetailByBackendRes,
  GetTermsVersionDetailByBackendApiRes,
} from './terms.interface';

@Injectable()
export class OperatorTermsServiceByBackend {
  private readonly url: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly apiService: ApiService,
  ) {
    this.url = `${this.configService.get(
      'shared.restful.backend.url',
    )}/operator/v1/terms`;
  }

  /**
   * @description [GET] /operator/v1/terms
   */
  async getTermsList({
    size,
    page,
  }: GetTermsListByBackendReq): Promise<GetTermsListByBackendRes> {
    const { terms, totalCount } = await lastValueFrom(
      this.apiService.request({
        provider: ApiProviderEnum.BACKEND,
        method: ApiMethodEnum.GET,
        url: this.url,
        config: {
          params: {
            size,
            page,
          },
        },
      }) as Observable<GetTermsListByBackendApiRes>,
    );
    return {
      termsList: terms.map((term) => ({
        versionId: term.versionID,
        operatorId: term.byOperatorID,
        createdAt: term.createdTime,
        name: term.name,
        requiredRead: term.requireRead,
        id: term.termID,
      })),
      totalCount,
    };
  }

  /**
   * @description [POST] /operator/v1/terms/{termID}
   */
  async createTermsVersion({
    operatorUserId,
    termsId,
    content,
    requiredRead,
    url,
  }: CreateTermsVersionByBackendRes): Promise<void> {
    await lastValueFrom(
      this.apiService.request({
        provider: ApiProviderEnum.BACKEND,
        method: ApiMethodEnum.POST,
        url: `${this.url}/${termsId}`,
        config: {
          data: {
            byOperator: operatorUserId,
            content,
            requireRead: requiredRead,
            url,
          },
        },
      }),
    );
  }

  /**
   * @description [GET] /operator/v1/terms/{termID}/versions
   */
  async getTermsVersionList({
    termsId,
    size,
    page,
  }: GetTermsVersionListByBackendReq): Promise<GetTermsVersionListByBackendRes> {
    const { versions, totalCount } = await lastValueFrom(
      this.apiService.request({
        provider: ApiProviderEnum.BACKEND,
        method: ApiMethodEnum.GET,
        url: `${this.url}/${termsId}/versions`,
        config: {
          params: {
            size,
            page,
          },
        },
      }) as Observable<GetTermsVersionListByBackendApiRes>,
    );
    return {
      versions: versions.map((val) => ({
        id: val.versionID,
        operatorId: val.byOperatorID,
        createdAt: val.createdTime,
        name: val.name,
        requiredRead: val.requireRead,
        termsId: val.termID,
      })),
      totalCount,
    };
  }

  /**
   * @description [GET] /operator/v1/terms/{termID}/version/{versionID}
   */
  async getTermsVersionDetail({
    termsId,
    versionId,
  }: GetTermsVersionDetailByBackendReq): Promise<GetTermsVersionDetailByBackendRes> {
    const version = await lastValueFrom(
      this.apiService.request({
        provider: ApiProviderEnum.BACKEND,
        method: ApiMethodEnum.GET,
        url: `${this.url}/${termsId}/version/${versionId}`,
      }) as Observable<GetTermsVersionDetailByBackendApiRes>,
    );
    return {
      versionId: version.versionID,
      operatorId: version.byOperatorID,
      content: version.content,
      createdAt: version.createdTime,
      name: version.name,
      requiredRead: version.requireRead,
      termsId: version.termID,
      url: version.url,
    };
  }
}
