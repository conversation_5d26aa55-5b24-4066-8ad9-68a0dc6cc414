export interface GetTermsListByBackendReq {
  size: number;
  page: number;
}
export interface GetTermsListByBackendApiRes {
  terms: {
    versionID: string;
    byOperatorID: number;
    createdTime: number;
    name: string;
    requireRead: boolean;
    termID: number;
    url: string;
  }[];
  totalCount: number;
}
export interface GetTermsListByBackendRes {
  termsList: {
    versionId: string;
    operatorId: number;
    createdAt: number;
    name: string;
    requiredRead: boolean;
    id: number;
  }[];
  totalCount: number;
}

export interface GetTermsVersionListByBackendReq {
  termsId: number;
  size: number;
  page: number;
}
export interface GetTermsVersionListByBackendApiRes {
  versions: {
    versionID: string;
    byOperatorID: number;
    content: string;
    createdTime: number;
    name: string;
    requireRead: boolean;
    termID: number;
    url: string;
  }[];
  totalCount: number;
}
export interface GetTermsVersionListByBackendRes {
  versions: {
    id: string;
    operatorId: number;
    createdAt: number;
    name: string;
    requiredRead: boolean;
    termsId: number;
  }[];
  totalCount: number;
}

export interface CreateTermsVersionByBackendRes {
  operatorUserId: number;
  termsId: number;
  content: string;
  requiredRead: boolean;
  url?: string;
}

export interface GetTermsVersionDetailByBackendReq {
  termsId: number;
  versionId: string;
}
export interface GetTermsVersionDetailByBackendApiRes {
  versionID: string;
  byOperatorID: number;
  content: string;
  createdTime: number;
  name: string;
  requireRead: boolean;
  termID: number;
  url: string;
}
export interface GetTermsVersionDetailByBackendRes {
  versionId: string;
  operatorId: number;
  content: string;
  createdAt: number;
  name: string;
  requiredRead: boolean;
  termsId: number;
  url: string;
}
