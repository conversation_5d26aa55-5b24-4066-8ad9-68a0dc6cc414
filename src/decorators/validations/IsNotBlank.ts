// NOTE: 自定義欄位檢查
import { registerDecorator, ValidationOptions } from 'class-validator';

export function isNotBlank(value: unknown): boolean {
  return typeof value === 'string' && value.trim().length > 0;
}

export function IsNotBlank(validationOptions?: ValidationOptions) {
  return (object: object, propertyName: string) => {
    registerDecorator({
      name: 'IsNotBlank',
      target: object.constructor,
      propertyName,
      constraints: [],
      options: {
        ...validationOptions,
        message: `${propertyName} is not properly formatted`,
      },
      validator: {
        validate: (value): boolean => isNotBlank(value),
      },
    });
  };
}
