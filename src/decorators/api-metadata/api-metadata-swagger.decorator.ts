import { applyDecorators } from '@nestjs/common';
import { ApiHeader } from '@nestjs/swagger';
import { ApiMetadataService } from 'src/decorators/api-metadata/api-metadata.service';
export function ApiMetadataSwagger(): MethodDecorator & ClassDecorator {
  const apiMetadataService = new ApiMetadataService();
  const headerDefinitions = apiMetadataService.getSwaggerHeaderDefinitions();
  const decorators = Object.entries(headerDefinitions).map(
    ([headerName, definition]) =>
      ApiHeader({
        name: headerName,
        description: definition.description,
        required: definition.required,
        schema: definition.schema,
      }),
  );
  return applyDecorators(...decorators);
}
export function ApiMetadataController(): MethodDecorator & ClassDecorator {
  return ApiMetadataSwagger();
}
export function ApiMetadataEndpoint(): MethodDecorator {
  return ApiMetadataSwagger();
}
