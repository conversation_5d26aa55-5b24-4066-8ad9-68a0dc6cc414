import { Injectable } from '@nestjs/common';
import { LanguageCodeEnum } from 'src/database/translation.entity';
import {
  ApiMetadataConfig,
  ApiMetadataHeaderEnum,
  ApiMetadataHeaders,
  DEFAULT_API_METADATA_CONFIG,
  RawApiMetadataHeaders,
} from 'src/decorators/api-metadata/api-metadata.interface';
import { v4 as uuidv4 } from 'uuid';
@Injectable()
export class ApiMetadataService {
  private readonly config: Required<ApiMetadataConfig> =
    DEFAULT_API_METADATA_CONFIG;
  processHeaders(rawHeaders: RawApiMetadataHeaders): ApiMetadataHeaders {
    return {
      language: this.validateLanguage(
        rawHeaders[ApiMetadataHeaderEnum.LANGUAGE],
      ),
      timezone: this.validateTimezone(
        rawHeaders[ApiMetadataHeaderEnum.TIMEZONE],
      ),
      requestId: this.validateRequestId(
        rawHeaders[ApiMetadataHeaderEnum.REQUEST_ID],
      ),
    };
  }
  extractRawHeaders(
    headers: Record<string, string | string[]>,
  ): RawApiMetadataHeaders {
    const getValue = (key: string): string | undefined => {
      const value = headers[key.toLowerCase()];
      return Array.isArray(value) ? value[0] : value;
    };
    return {
      [ApiMetadataHeaderEnum.LANGUAGE]: getValue(
        ApiMetadataHeaderEnum.LANGUAGE,
      ),
      [ApiMetadataHeaderEnum.TIMEZONE]: getValue(
        ApiMetadataHeaderEnum.TIMEZONE,
      ),
      [ApiMetadataHeaderEnum.REQUEST_ID]: getValue(
        ApiMetadataHeaderEnum.REQUEST_ID,
      ),
    };
  }
  private validateLanguage(language?: string): LanguageCodeEnum {
    if (!language) {
      return this.config.defaultLanguage;
    }
    const upperLanguage = language.toUpperCase();
    if (
      Object.values(LanguageCodeEnum).includes(
        upperLanguage as LanguageCodeEnum,
      )
    ) {
      return upperLanguage as LanguageCodeEnum;
    }
    const languageMap: Record<string, LanguageCodeEnum> = {
      ZHTW: LanguageCodeEnum.ZHTW,
      EN: LanguageCodeEnum.EN,
    };
    return languageMap[upperLanguage] || this.config.defaultLanguage;
  }
  private validateTimezone(timezone?: string): string {
    if (!timezone) {
      return this.config.defaultTimezone;
    }
    if (!this.config.timezonePattern.test(timezone)) {
      return this.config.defaultTimezone;
    }
    const timezoneMap: Record<string, string> = {
      GMT: 'UTC',
      'GMT+0': 'UTC',
      'GMT-0': 'UTC',
      Z: 'UTC',
      CST: 'Asia/Shanghai',
      EST: 'America/New_York',
      PST: 'America/Los_Angeles',
      JST: 'Asia/Tokyo',
    };
    return timezoneMap[timezone.toUpperCase()] || timezone;
  }
  private validateClientVersion(clientVersion?: string): string | undefined {
    if (!clientVersion) {
      return undefined;
    }
    const sanitized = clientVersion.replace(/[^a-zA-Z0-9.\-_]/g, '');
    if (!sanitized || sanitized.length > 50) {
      return undefined;
    }
    return sanitized;
  }
  private validateRequestId(requestId?: string): string {
    if (requestId) {
      const uuidRegex =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      const alphanumericRegex = /^[a-zA-Z0-9\-_]{8,64}$/;
      if (uuidRegex.test(requestId) || alphanumericRegex.test(requestId)) {
        return requestId;
      }
    }
    if (this.config.autoGenerateRequestId) {
      return uuidv4();
    }
    return requestId || 'unknown';
  }
  getSwaggerHeaderDefinitions(): Record<string, any> {
    return {
      [ApiMetadataHeaderEnum.LANGUAGE]: {
        description: 'Client language preference',
        schema: {
          type: 'string',
          enum: Object.values(LanguageCodeEnum),
          default: this.config.defaultLanguage,
          example: LanguageCodeEnum.EN,
        },
        required: false,
      },
      [ApiMetadataHeaderEnum.TIMEZONE]: {
        description:
          'Client timezone (IANA timezone identifier or common abbreviation)',
        schema: {
          type: 'string',
          default: this.config.defaultTimezone,
          example: 'UTC',
          pattern: this.config.timezonePattern.source,
        },
        required: false,
      },
      [ApiMetadataHeaderEnum.REQUEST_ID]: {
        description: 'Request ID for tracing (auto-generated if not provided)',
        schema: {
          type: 'string',
          example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
          pattern: '^[a-zA-Z0-9\\-_]{8,64}$',
        },
        required: false,
      },
    };
  }
}
