import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { Request } from 'express';
import {
  API_METADATA_KEY,
  ApiMetadataHeaders,
} from 'src/decorators/api-metadata/api-metadata.interface';
export const ApiMetadata = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): ApiMetadataHeaders => {
    const request = ctx.switchToHttp().getRequest<Request>();
    const metadata = request[API_METADATA_KEY] as ApiMetadataHeaders;
    if (!metadata) {
      throw new Error(
        'ApiMetadata decorator used but ApiMetadataInterceptor is not configured. ' +
          'Please ensure ApiMetadataInterceptor is registered globally in your application.',
      );
    }
    return metadata;
  },
);
