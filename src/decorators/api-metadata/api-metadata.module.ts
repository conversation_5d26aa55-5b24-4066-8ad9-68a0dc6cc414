import { Module, Global } from '@nestjs/common';
import { APP_INTERCEPTOR } from '@nestjs/core';
import { ApiMetadataService } from 'src/decorators/api-metadata/api-metadata.service';
import { ApiMetadataInterceptor } from 'src/interceptors/api-metadata.interceptor';

@Global()
@Module({
  providers: [
    ApiMetadataService,
    {
      provide: APP_INTERCEPTOR,
      useClass: ApiMetadataInterceptor,
    },
  ],
  exports: [ApiMetadataService],
})
export class ApiMetadataModule {}
