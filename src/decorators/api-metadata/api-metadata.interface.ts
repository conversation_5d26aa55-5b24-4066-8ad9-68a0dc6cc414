import { LanguageCodeEnum } from 'src/database/translation.entity';
export enum ApiMetadataHeaderEnum {
  LANGUAGE = 'x-lang',
  TIMEZONE = 'x-timezone',
  REQUEST_ID = 'x-request-id',
}
export interface ApiMetadataHeaders {
  language: LanguageCodeEnum;
  timezone: string;
  requestId: string;
}
export interface RawApiMetadataHeaders {
  [ApiMetadataHeaderEnum.LANGUAGE]?: string;
  [ApiMetadataHeaderEnum.TIMEZONE]?: string;
  [ApiMetadataHeaderEnum.REQUEST_ID]?: string;
}
export interface ApiMetadataConfig {
  defaultLanguage?: LanguageCodeEnum;
  defaultTimezone?: string;
  autoGenerateRequestId?: boolean;
  timezonePattern?: RegExp;
}
export const API_METADATA_KEY = Symbol('API_METADATA');
export const DEFAULT_API_METADATA_CONFIG: Required<ApiMetadataConfig> = {
  defaultLanguage: LanguageCodeEnum.EN,
  defaultTimezone: 'UTC',
  autoGenerateRequestId: true,
  timezonePattern: /^[A-Za-z_\/\-\+0-9:]+$/,
};
