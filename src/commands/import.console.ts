import { Command, Console } from 'nestjs-console';
import { ImportService } from './import.service';

@Console()
export class ImportConsole {
  constructor(private importService: ImportService) {}

  @Command({
    command: 'import:permission-data',
  })
  async importPermissionData(): Promise<void> {
    try {
      await this.importService.importPermissionData();
      await this.importService.updateAdminRolePermissions();
    } catch (error) {
      throw error;
    }
  }

  @Command({
    command: 'import:operation-data',
  })
  async importOperationData(): Promise<void> {
    try {
      await this.importService.importOperationData();
    } catch (error) {
      throw error;
    }
  }

  @Command({
    command: 'import:admin-role',
  })
  async importAdminRole(): Promise<void> {
    try {
      await this.importService.importAdminRole();
      await this.importService.updateAdminRolePermissions();
    } catch (error) {
      throw error;
    }
  }

  @Command({
    command: 'import:admin-user',
  })
  async importAdminUser(): Promise<void> {
    try {
      await this.importService.importAdminUser();
    } catch (error) {
      throw error;
    }
  }
}
