import { InjectRepository } from '@nestjs/typeorm';
import { PermissionAction } from '../database/permission_action.entity';
import { PermissionObject } from '../database/permission_object.entity';
import { PermissionConfig } from '../modules/permission-object-groups/permission-object-groups.interface';
import { Repository, DataSource } from 'typeorm';
import { PermissionObjectGroup } from '../database/permission_object_group.entity';
import { Inject, Injectable } from '@nestjs/common';
import { Role } from '../database/role.entity';
import { User } from '../database/user.entity';
import { adminRoleId, adminRoleInfo } from './seeds/admin-role.seed';
import { adminUserId, adminUserInfo } from './seeds/admin-user.seed';
import { Enforcer } from 'casbin';
import { OperationObjectGroup } from '../database/operation_object_group.entity';
import { OperationObject } from '../database/operation_object.entity';
import { Operation } from '../database/operation.entity';
import { OperationEvent } from '../database/operation_event.entity';
import { OperationConfig } from '../modules/operation-object-groups/operation-object-groups.interface';
import * as bcrypt from 'bcrypt';
import { OperationLogTargetTypeEnum } from '../database/operation_log.entity';

@Injectable()
export class ImportService {
  constructor(
    @InjectRepository(PermissionObjectGroup)
    private permissionObjectGroupRepository: Repository<PermissionObjectGroup>,
    @InjectRepository(PermissionObject)
    private permissionObjectRepository: Repository<PermissionObject>,
    @InjectRepository(PermissionAction)
    private permissionActionRepository: Repository<PermissionAction>,
    @InjectRepository(OperationObjectGroup)
    private operationObjectGroupRepository: Repository<OperationObjectGroup>,
    @InjectRepository(OperationObject)
    private operationObjectRepository: Repository<OperationObject>,
    @InjectRepository(Operation)
    private operationRepository: Repository<Operation>,
    @InjectRepository(OperationEvent)
    private operationEventRepository: Repository<OperationEvent>,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @Inject('CASBIN_ENFORCER')
    private enforcer: Enforcer,
    private dataSource: DataSource,
  ) {}

  async updateAdminRolePermissions(): Promise<void> {
    try {
      const adminRole = await this.roleRepository.findOne({
        where: { id: adminRoleId },
      });
      if (adminRole) {
        await this.enforcer.deletePermissionsForUser(`role::${adminRoleId}`);
        const rules = [];
        Object.values(PermissionConfig).forEach((permissionObjectGroup) => {
          Object.values(permissionObjectGroup).forEach((object) => {
            if (typeof object === 'object') {
              Object.values(object).forEach((action) => {
                if (
                  typeof action === 'object' &&
                  action.hasOwnProperty('permissionActionKey')
                ) {
                  const {
                    permissionObjectKey: permissionObjectKey,
                    permissionActionKey: permissionActionKey,
                  } = action as any;
                  rules.push([
                    `role::${adminRoleId}`,
                    permissionObjectKey,
                    permissionActionKey,
                  ]);
                }
              });
            }
          });
        });
        this.enforcer.addPolicies(rules);
      }
    } catch (error) {
      throw error;
    }
  }

  async importPermissionData(): Promise<void> {
    try {
      const permissionObjectGroups: PermissionObjectGroup[] = [];
      const permissionObjects: PermissionObject[] = [];
      const permissionActions: PermissionAction[] = [];

      Object.values(PermissionConfig).forEach((permissionObjectGroup) => {
        const {
          permissionObjectGroupKey,
          permissionObjectGroupName,
          permissionObjectGroupPosition,
        } = permissionObjectGroup;
        permissionObjectGroups.push({
          key: permissionObjectGroupKey,
          name: permissionObjectGroupName,
          position: permissionObjectGroupPosition,
        });

        Object.values(permissionObjectGroup).forEach((permissionObject) => {
          if (
            typeof permissionObject === 'object' &&
            permissionObject.hasOwnProperty('permissionObjectKey')
          ) {
            const {
              permissionObjectKey,
              permissionObjectName,
              permissionObjectPosition,
              permissionObjectDescription,
            } = permissionObject;
            permissionObjects.push({
              key: permissionObjectKey,
              name: permissionObjectName,
              position: permissionObjectPosition,
              description: permissionObjectDescription,
              permissionObjectGroupKey,
            });

            Object.values(permissionObject).forEach((permissionAction) => {
              if (
                typeof permissionAction === 'object' &&
                permissionAction.hasOwnProperty('permissionActionKey')
              ) {
                const {
                  permissionActionKey,
                  permissionActionName,
                  permissionActionPosition,
                  permissionActionDescription,
                  permissionRequiredActionKeys,
                } = permissionAction as any;
                permissionActions.push({
                  key: permissionActionKey,
                  name: permissionActionName,
                  position: permissionActionPosition,
                  description: permissionActionDescription,
                  requiredKeys: permissionRequiredActionKeys,
                  permissionObjectKey,
                });
              }
            });
          }
        });
      });

      await this.permissionObjectGroupRepository.save(permissionObjectGroups);
      await this.permissionObjectRepository.save(permissionObjects);
      await this.permissionActionRepository.save(permissionActions);
    } catch (error) {
      throw error;
    }
  }

  async importOperationData(): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    try {
      await queryRunner.connect();
      const currentOperationLogTargetTypes = (
        await queryRunner.query(`
        SELECT e.enumlabel as "enumValue"
        FROM pg_type t
        JOIN pg_enum e ON t.oid = e.enumtypid
        WHERE t.typname = 'operation_log_target_type';
      `)
      ).map((row: { enumValue: string }) => row.enumValue);
      const operationLogTargetTypes = Object.values(OperationLogTargetTypeEnum);
      for (const operationLogTargetType of operationLogTargetTypes) {
        if (!currentOperationLogTargetTypes.includes(operationLogTargetType)) {
          await queryRunner.query(
            `ALTER TYPE "operation_log_target_type" ADD VALUE '${operationLogTargetType}'`,
          );
        }
      }

      const operationObjectGroups: OperationObjectGroup[] = [];
      const operationObjects: OperationObject[] = [];
      const operations: Operation[] = [];
      const operationEvents: OperationEvent[] = [];

      Object.values(OperationConfig).forEach((operationObjectGroup) => {
        const {
          operationObjectGroupKey,
          operationObjectGroupName,
          operationObjectPosition,
        } = operationObjectGroup;
        operationObjectGroups.push({
          key: operationObjectGroupKey,
          name: operationObjectGroupName,
          position: operationObjectPosition,
        });

        Object.values(operationObjectGroup).forEach((operationObject) => {
          if (
            typeof operationObject === 'object' &&
            operationObject.hasOwnProperty('operationObjectKey')
          ) {
            const {
              operationObjectKey,
              operationObjectName,
              operationObjectPosition,
            } = operationObject;
            operationObjects.push({
              key: operationObjectKey,
              name: operationObjectName,
              position: operationObjectPosition,
              operationObjectGroupKey,
            });

            Object.values(operationObject).forEach((operation) => {
              if (
                typeof operation === 'object' &&
                operation.hasOwnProperty('operationKey')
              ) {
                const { operationKey, operationName, operationPosition } =
                  operation as any;
                operations.push({
                  key: operationKey,
                  name: operationName,
                  position: operationPosition,
                  operationObjectKey,
                });

                Object.values(operation).forEach((operationEvent) => {
                  if (
                    typeof operationEvent === 'object' &&
                    operationEvent.hasOwnProperty('operationEventKey')
                  ) {
                    const {
                      operationEventKey,
                      operationEventName,
                      operationEventPosition,
                    } = operationEvent as any;
                    operationEvents.push({
                      key: operationEventKey,
                      name: operationEventName,
                      position: operationEventPosition,
                      operationKey,
                    });
                  }
                });
              }
            });
          }
        });
      });

      await this.operationObjectGroupRepository.save(operationObjectGroups);
      await this.operationObjectRepository.save(operationObjects);
      await this.operationRepository.save(operations);
      await this.operationEventRepository.save(operationEvents);
    } catch (error) {
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async importAdminRole(): Promise<void> {
    try {
      const adminRole = await this.roleRepository.findOne({
        where: { id: adminRoleId },
      });
      if (!adminRole) {
        await this.roleRepository.save({
          id: adminRoleId,
          name: adminRoleInfo.name,
          description: adminRoleInfo.description,
          creatorUserId: adminRoleInfo.creatorUserId,
          updaterUserId: adminRoleInfo.updaterUserId,
        });
      }
    } catch (error) {
      throw error;
    }
  }

  async importAdminUser(): Promise<void> {
    try {
      const adminUser = await this.userRepository.findOne({
        where: { id: adminUserId },
      });
      if (!adminUser) {
        await this.userRepository.save({
          id: adminUserId,
          email: adminUserInfo.email,
          firstName: adminUserInfo.firstName,
          lastName: adminUserInfo.lastName,
          department: adminUserInfo.department,
          title: adminUserInfo.title,
          status: adminUserInfo.status,
          creatorUserId: adminUserInfo.creatorUserId,
          updaterUserId: adminUserInfo.updaterUserId,
          password: await bcrypt.hash(adminUserInfo.password, 10),
          passwordLoginAllowed: adminUserInfo.passwordLoginAllowed,
        });
        this.enforcer.addRoleForUser(
          `user::${adminUserId}`,
          `role::${adminRoleId}`,
        );
      }
    } catch (error) {
      throw error;
    }
  }
}
