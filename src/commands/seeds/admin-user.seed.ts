import { User, UserStatusEnum } from '../../database/user.entity';

export const adminUserId = 1;

// MEMO: 預設可以改為建置人員的資訊
export const adminUserInfo: User = {
  id: adminUserId,
  email: '<EMAIL>',
  firstName: '系統管理員',
  lastName: 'Admin',
  department: '產品研發部門',
  title: 'RD',
  status: UserStatusEnum.VERIFIED,
  creatorUserId: adminUserId,
  updaterUserId: adminUserId,
  // MEMO: 初次匯入時需要新增密碼（明碼）及允許使用密碼登入
};
