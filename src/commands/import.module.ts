import { Module } from '@nestjs/common';
import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';
import { ConsoleModule } from 'nestjs-console';
import { PermissionAction } from '../database/permission_action.entity';
import { PermissionObjectGroup } from '../database/permission_object_group.entity';
import { PermissionObject } from '../database/permission_object.entity';
import { ImportService } from './import.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import database from '../config/database.config';
import { ImportConsole } from './import.console';
import { Role } from '../database/role.entity';
import { User } from '../database/user.entity';
import { CasbinModule } from '../modules/casbin/casbin.module';
import { OperationEvent } from '../database/operation_event.entity';
import { OperationObjectGroup } from '../database/operation_object_group.entity';
import { OperationObject } from '../database/operation_object.entity';
import { Operation } from '../database/operation.entity';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [database],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return {
          ...configService.get<TypeOrmModuleOptions>('database'),
          retryAttempts: 3,
          retryDelay: 1000,
        };
      },
    }),
    TypeOrmModule.forFeature([
      PermissionObjectGroup,
      PermissionObject,
      PermissionAction,
      OperationObjectGroup,
      OperationObject,
      Operation,
      OperationEvent,
      Role,
      User,
    ]),
    ConsoleModule,
    CasbinModule,
  ],
  controllers: [],
  providers: [ImportConsole, ImportService],
})
export class ImportDataModule {}
