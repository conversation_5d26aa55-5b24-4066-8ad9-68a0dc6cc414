import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';
import auth from './config/auth.config';
import base from './config/base.config';
import okta from './config/okta.config';
import database from './config/database.config';
import regex from './config/regex.config';
import shared from './config/shared.config';
import sendMail from './config/send-mail.config';
import unleash from './config/unleash.config';
import { HealthzModule } from './modules/healthz/health.module';
import { CasbinModule } from './modules/casbin/casbin.module';
import { AuthModule } from './modules/auth/auth.module';
import { OperationLogsModule } from './modules/operation-logs/operation-logs.module';
import { PermissionObjectGroupsModule } from './modules/permission-object-groups/permission-object-groups.module';
import { RolesModule } from './modules/roles/roles.module';
import { UsersModule } from './modules/users/users.module';
import { AppUsersModule } from './modules/app-users/app-users.module';
import { TermsModule } from './modules/terms/terms.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [base, auth, database, okta, regex, shared, sendMail, unleash],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) =>
        configService.get<TypeOrmModuleOptions>('database'),
    }),
    HealthzModule,
    CasbinModule,
    AuthModule,
    OperationLogsModule,
    PermissionObjectGroupsModule,
    RolesModule,
    UsersModule,
    AppUsersModule,
    TermsModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
