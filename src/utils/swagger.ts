import * as fs from 'fs';
import * as path from 'path';
import * as yaml from 'js-yaml';
import { ConfigService } from '@nestjs/config';
/**
 * ＠description 生成欄位描述
 * @param {string} basic 欄位基本描述
 * @param {string} defaultValue 欄位的預設值
 * @param {string[]} enumTypeDescription 列舉 enum 所對應的意義
 * @param {string[]} limitations 限制
 * @param {string[]} remarks 備註
 */
export function customApiPropertyDescription({
  basic,
  defaultValue = '',
  enumTypeDescription = [],
  limitations = [],
  remarks = [],
}: {
  basic: string;
  defaultValue?: string;
  enumTypeDescription?: string[];
  limitations?: string[];
  remarks?: string[];
}): string {
  let description = `${basic}`;

  if (defaultValue) {
    description = `${description}<br />預設值：${defaultValue}`;
  }

  if (enumTypeDescription.length) {
    description = `${description}<br />列舉<br />• ${enumTypeDescription.join(
      '<br />• ',
    )}`;
  }

  if (limitations.length) {
    description = `${description}<br />限制<br />• ${limitations.join(
      '<br />• ',
    )}`;
  }

  if (remarks.length) {
    description = `${description}<br />備註<br />• ${remarks.join('<br />• ')}`;
  }

  return description;
}

/**
 * ＠description 生成 API 描述
 * @param {string} permissions 權限
 * @param {string[]} operationLogs 操作記錄
 */
export function customApiOperationDescription({
  permissions = [],
  operationLogs = [],
  relatedBEApis = [],
}: {
  permissions?: string[];
  operationLogs?: string[];
  relatedBEApis?: string[];
}): string {
  const descriptions: string[] = [];

  if (permissions.length) {
    descriptions.push(`權限<br />• ${permissions.join('<br />• ')}`);
  }

  if (operationLogs.length) {
    descriptions.push(`操作記錄<br />• ${operationLogs.join('<br />• ')}`);
  }

  if (relatedBEApis.length) {
    descriptions.push(`相關 BE API<br />• ${relatedBEApis.join('<br />• ')}`);
  }

  return descriptions.join('<br />') || '';
}

/**
 * ＠description 生成 API 回應描述
 * @param {string} basic 基本描述
 * @param {string[]} remarks 備註
 */
export function customApiResponseDescription({
  basic,
  remarks = [],
}: {
  basic: string;
  remarks?: string[];
}): string {
  let description = `${basic}`;

  if (remarks.length) {
    description = `${description}<br />備註<br />• ${remarks.join('<br />• ')}`;
  }

  return description;
}

/**
 * Export Swagger documentation to local files
 * @param swaggerDocument - The generated Swagger document
 * @param configService - NestJS ConfigService instance
 */
export function exportSwaggerDocs(
  swaggerDocument: any,
  configService: ConfigService,
): void {
  try {
    // Create docs directory if it doesn't exist
    const docsDir = path.join(process.cwd(), 'docs', 'swagger');
    if (!fs.existsSync(docsDir)) {
      fs.mkdirSync(docsDir, { recursive: true });
    }

    // Generate timestamp for versioning
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const version =
      configService.get<string>('swaggerDocumentVersion') || '1.0.0';
    const env = configService.get<string>('appEnv') || 'dev';

    // Export as JSON
    const jsonFileName = `swagger-${env}-v${version}-${timestamp}.json`;
    const jsonFilePath = path.join(docsDir, jsonFileName);
    fs.writeFileSync(jsonFilePath, JSON.stringify(swaggerDocument, null, 2));

    // Export as YAML
    const yamlFileName = `swagger-${env}-v${version}-${timestamp}.yaml`;
    const yamlFilePath = path.join(docsDir, yamlFileName);
    const yamlContent = yaml.dump(swaggerDocument, {
      indent: 2,
      lineWidth: -1,
      noRefs: true,
      sortKeys: false,
    });
    fs.writeFileSync(yamlFilePath, yamlContent);

    // Create/update latest files (without timestamp)
    const latestJsonPath = path.join(docsDir, `swagger-${env}-latest.json`);
    const latestYamlPath = path.join(docsDir, `swagger-${env}-latest.yaml`);

    fs.writeFileSync(latestJsonPath, JSON.stringify(swaggerDocument, null, 2));
    fs.writeFileSync(latestYamlPath, yamlContent);

    // Create index file with metadata
    const indexData = {
      generated: new Date().toISOString(),
      environment: env,
      version: version,
      title: configService.get<string>('swaggerDocumentTitle'),
      description: configService.get<string>('swaggerDocumentDescription'),
      files: {
        json: {
          latest: `swagger-${env}-latest.json`,
          versioned: jsonFileName,
        },
        yaml: {
          latest: `swagger-${env}-latest.yaml`,
          versioned: yamlFileName,
        },
      },
      endpoints: {
        total: Object.keys(swaggerDocument.paths || {}).length,
        paths: Object.keys(swaggerDocument.paths || {}),
      },
    };

    const indexPath = path.join(docsDir, `index-${env}.json`);
    fs.writeFileSync(indexPath, JSON.stringify(indexData, null, 2));

    console.log(`✅ Swagger documentation exported to:`);
    console.log(`   📄 JSON: ${jsonFilePath}`);
    console.log(`   📄 YAML: ${yamlFilePath}`);
    console.log(`   📄 Latest JSON: ${latestJsonPath}`);
    console.log(`   📄 Latest YAML: ${latestYamlPath}`);
    console.log(`   📄 Index: ${indexPath}`);
  } catch (error) {
    console.error('❌ Failed to export Swagger documentation:', error.message);
  }
}
