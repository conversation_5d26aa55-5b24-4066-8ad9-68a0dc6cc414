/**
 * @description 型別 string 轉換為 number，若無法轉換則回傳 null
 * @param {string}
 * @returns {number | null}
 **/
export function stringToNumber(value: string): number | null {
  return Number(value) ? parseInt(value, 10) : null;
}

/**
 * @description 型別 bigint 轉換為 number，若無法轉換則回傳 null
 * @param {bigint}
 * @returns {number | null}
 **/
export function bigintToNumber(value: bigint): number | null {
  return value
    ? Number(value.toString())
      ? parseInt(value.toString(), 10)
      : null
    : null;
}

/**
 * ＠description 組合完整的使用者姓名
 * @param {string} firstName - 使用者名字
 * @param {string} lastName - 使用者姓氏
 * @returns {string} 完整的姓名
 */
export function getFullName(firstName: string, lastName: string): string {
  return `${lastName}${firstName}`;
}

/**
 * @description 取得分頁跳過數量（DB 搜尋用）
 * @param {number} size - 原始每頁數量
 * @param {number} page - 原始頁數（從 1 開始）
 * @returns {number} 跳過數量
 */
export function getPagingSkip(size: number, page: number): number {
  const pageSize = Math.max(0, size);
  const pageNumber = Math.max(0, page - 1);
  return pageSize * pageNumber;
}

/**
 * @description 取得分頁取值數量（DB 搜尋用）
 * @param {number} size - 原始每頁數量
 * @returns {number} 取值數量
 */
export function getPagingTake(size: number): number {
  return Math.max(0, size);
}
