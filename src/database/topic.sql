CREATE TABLE IF NOT EXISTS
    topic (
        id SERIAL PRIMARY KEY,
        name TEXT NOT NULL,
        creator_user_id INTEGER NOT NULL,
        updater_user_id INTEGER NOT NULL,
        created_at BIGINT NOT NULL DEFAULT TRUNC(
            (
                EXTRACT(
                    epoch
                    FROM
                        NOW()
                ) * (1000)::NUMERIC
            )
        ),
        updated_at BIGINT NOT NULL DEFAULT TRUNC(
            (
                EXTRACT(
                    epoch
                    FROM
                        NOW()
                ) * (1000)::NUMERIC
            )
        ),
        deleted_at BIGINT,
        deleter_user_id INTEGER
    );

CREATE UNIQUE INDEX "idx_unique_topic_name" ON topic USING btree (name, deleted_at);