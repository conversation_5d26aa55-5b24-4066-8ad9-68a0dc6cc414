CREATE TABLE role (
  id                 SERIAL PRIMARY KEY,
  name               varchar(30) NOT NULL,
  description        text,
  creator_user_id    integer NOT NULL,
  updater_user_id    integer NOT NULL,
  created_at         bigint NOT NULL DEFAULT TRUNC(EXTRACT(EPOCH FROM NOW()) * 1000),
  updated_at         bigint NOT NULL DEFAULT TRUNC(EXTRACT(EPOCH FROM NOW()) * 1000),
  deleted_at         bigint,
  deleter_user_id    integer
);

COMMENT ON COLUMN "role"."id" IS '角色 ID';

COMMENT ON COLUMN "role"."name" IS '角色名稱';

COMMENT ON COLUMN "role"."description" IS '角色描述';

COMMENT ON COLUMN "role"."created_at" IS '新增角色的時間 (unix timestamp)';

COMMENT ON COLUMN "role"."creator_user_id" IS '新增角色的使用者 ID';

COMMENT ON COLUMN "role"."updated_at" IS '更新角色的時間 (unix timestamp)';

COMMENT ON COLUMN "role"."updater_user_id" IS '更新角色的使用者 ID';

COMMENT ON COLUMN "role"."deleted_at" IS '刪除角色的時間 (unix timestamp)';

COMMENT ON COLUMN "role"."deleter_user_id" IS '刪除角色的使用者 ID';