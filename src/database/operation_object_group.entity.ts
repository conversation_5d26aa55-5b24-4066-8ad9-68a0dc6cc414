import { Column, <PERSON><PERSON>ty, OneToMany, PrimaryColumn } from 'typeorm';
import { OperationObject } from './operation_object.entity';

@Entity('operation_object_group')
export class OperationObjectGroup {
  @PrimaryColumn({
    name: 'key',
    type: 'text',
  })
  key: string;

  @Column({
    name: 'name',
    type: 'text',
  })
  name: string;

  @Column({
    name: 'position',
    type: 'integer',
  })
  position: number;

  @OneToMany(() => OperationObject, (object) => object.operationObjectGroup)
  operationObjects?: OperationObject[];
}
