import { Column, <PERSON>tity, <PERSON>in<PERSON><PERSON><PERSON>n, ManyToOne, PrimaryColumn } from 'typeorm';
import { Operation } from './operation.entity';

@Entity('operation_event')
export class OperationEvent {
  @PrimaryColumn({
    name: 'key',
    type: 'text',
  })
  key: string;

  @Column({
    name: 'name',
    type: 'text',
  })
  name: string;

  @Column({
    name: 'position',
    type: 'integer',
  })
  position: number;

  @Column({
    name: 'operation_key',
    type: 'text',
  })
  operationKey: string;

  @ManyToOne(() => Operation, (operation) => operation.operationEvents)
  @JoinColumn({
    name: 'operation_key',
  })
  operation?: Operation;
}
