import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { User } from './user.entity';

@Entity('role')
export class Role {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    name: 'name',
    type: 'varchar',
    length: 30,
  })
  name: string;

  @Column({
    name: 'description',
    type: 'text',
    nullable: true,
  })
  description?: string;

  @Column({ name: 'creator_user_id', type: 'integer' })
  creatorUserId: number;

  @Column({ name: 'updater_user_id', type: 'integer' })
  updaterUserId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'creator_user_id' })
  creator?: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updater_user_id' })
  updater?: User;

  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => 'TRUNC(EXTRACT(EPOCH from NOW()) * 1000)',
  })
  createdAt?: bigint;

  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => 'TRUNC(EXTRACT(EPOCH from NOW()) * 1000)',
  })
  updatedAt?: bigint;

  @Column({
    name: 'deleted_at',
    type: 'bigint',
    nullable: true,
  })
  deletedAt?: bigint;

  @Column({
    name: 'deleter_user_id',
    type: 'integer',
    nullable: true,
  })
  deleterUserId?: number;

  @ManyToOne(() => User)
  @JoinColumn({
    name: 'deleter_user_id',
  })
  deleter?: User;
}
