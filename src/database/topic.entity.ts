import {
  Column,
  <PERSON>tity,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';

import { User } from 'src/database/user.entity';
import { PageTopic } from 'src/database/page_topic.entity';

export const TOPIC_DATA_TRANSLATION_FIELD_NAME = 'name';
export const TOPIC_TABLE_NAME = 'topic';
@Entity(TOPIC_TABLE_NAME)
@Index('idx_unique_topic_name', ['name'], {
  unique: true,
  where: 'deleted_at IS NULL',
})
export class Topic {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    name: 'name',
    type: 'text',
  })
  name: string;

  @Column({
    name: 'creator_user_id',
    type: 'integer',
  })
  creatorUserId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'creator_user_id' })
  creator?: User;

  @Column({
    name: 'updater_user_id',
    type: 'integer',
  })
  updaterUserId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updater_user_id' })
  updater?: User;

  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => 'TRUNC(EXTRACT(EPOCH from NOW()) * 1000)',
  })
  createdAt?: bigint;

  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => 'TRUNC(EXTRACT(EPOCH from NOW()) * 1000)',
  })
  updatedAt?: bigint;

  @Column({
    name: 'deleted_at',
    type: 'bigint',
    nullable: true,
  })
  deletedAt?: bigint;

  @Column({
    name: 'deleter_user_id',
    type: 'integer',
    nullable: true,
  })
  deleterUserId?: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'deleter_user_id' })
  deleter?: User;

  @OneToMany(() => PageTopic, (topicPage) => topicPage.topic)
  topicPages?: PageTopic[];
}
