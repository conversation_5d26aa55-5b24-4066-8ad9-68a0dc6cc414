import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON><PERSON>,
  PrimaryGeneratedColumn,
} from 'typeorm';

export enum UserStatusEnum {
  UNVERIFIED = 'UNVERIFIED', // MEMO: 未驗證
  VERIFIED = 'VERIFIED', // MEMO: 已驗證
  SUSPENDED = 'SUSPENDED', // MEMO: 已停用
}

@Entity('user')
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    name: 'email',
    type: 'varchar',
    length: 320,
  })
  email: string;

  @Column({
    name: 'first_name',
    type: 'text',
  })
  firstName: string;

  @Column({
    name: 'last_name',
    type: 'text',
  })
  lastName: string;

  @Column({
    name: 'department',
    type: 'text',
  })
  department: string;

  @Column({
    name: 'title',
    type: 'text',
  })
  title: string;

  @Column({
    name: 'status',
    type: 'enum',
    enum: UserStatusEnum,
  })
  status: UserStatusEnum;

  @Column({
    type: 'text',
    nullable: true,
  })
  password?: string;

  @Column({
    name: 'set_password_token',
    type: 'text',
    nullable: true,
  })
  setPasswordToken?: string;

  @Column({
    name: 'reset_password_token',
    type: 'text',
    nullable: true,
  })
  resetPasswordToken?: string;

  @Column({
    name: 'password_login_allowed',
    type: 'boolean',
    nullable: true,
  })
  passwordLoginAllowed?: boolean;

  @Column({
    name: 'creator_user_id',
    type: 'integer',
  })
  creatorUserId: number;

  @ManyToOne(() => User)
  @JoinColumn({
    name: 'creator_user_id',
  })
  creator?: User;

  @Column({
    name: 'updater_user_id',
    type: 'integer',
  })
  updaterUserId: number;

  @ManyToOne(() => User)
  @JoinColumn({
    name: 'updater_user_id',
  })
  updater?: User;

  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => 'TRUNC(EXTRACT(EPOCH from NOW()) * 1000)',
  })
  createdAt?: bigint;

  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => 'TRUNC(EXTRACT(EPOCH from NOW()) * 1000)',
  })
  updatedAt?: bigint;

  @Column({
    name: 'deleted_at',
    type: 'bigint',
    nullable: true,
  })
  deletedAt?: bigint;

  @Column({
    name: 'deleter_user_id',
    type: 'integer',
    nullable: true,
  })
  deleterUserId?: number;

  @ManyToOne(() => User)
  @JoinColumn({
    name: 'deleter_user_id',
  })
  deleter?: User;
}
