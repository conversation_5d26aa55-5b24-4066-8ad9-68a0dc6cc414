CREATE TYPE operation_log_target_type AS ENUM (
    'ROLE',
    'USER',
    'TERMS'
);

CREATE TABLE operation_log (
    id bigserial PRIMARY KEY,
    operation_key text NOT NULL,
    operation_event_key text,
    target_type operation_log_target_type,
    target_id text,
    target_name text,
    input_data jsonb,
    remarks text[],
    is_success boolean NOT NULL,
    target_original_data jsonb,
    output_data jsonb,
    fail_code integer,
    fail_reason text,
    created_at bigint NOT NULL DEFAULT TRUNC (
        EXTRACT(
            EPOCH
            FROM
                NOW ()
        ) * 1000
    ),
    creator_user_id integer NOT NULL
);

COMMENT ON COLUMN "operation_log"."id" IS '操作記錄 ID';

COMMENT ON COLUMN "operation_log"."operation_key" IS '操作項目 Key';

COMMENT ON COLUMN "operation_log"."operation_event_key" IS '操作事件 Key';

COMMENT ON COLUMN "operation_log"."target_type" IS '操作類型（USER: 使用者）';

COMMENT ON COLUMN "operation_log"."target_id" IS '操作目標 ID';

COMMENT ON COLUMN "operation_log"."target_name" IS '操作目標名稱';

COMMENT ON COLUMN "operation_log"."input_data" IS '操作所寫入的資訊';

COMMENT ON COLUMN "operation_log"."remarks" IS '操作備註與原因';

COMMENT ON COLUMN "operation_log"."is_success" IS '操作是否成功';

COMMENT ON COLUMN "operation_log"."target_original_data" IS '操作目標原始的資訊';

COMMENT ON COLUMN "operation_log"."output_data" IS '操作所回應的資訊';

COMMENT ON COLUMN "operation_log"."fail_code" IS '操作所回應的錯誤代碼';

COMMENT ON COLUMN "operation_log"."fail_reason" IS '操作所回應的錯誤訊息';

COMMENT ON COLUMN "operation_log"."created_at" IS '操作時間';

COMMENT ON COLUMN "operation_log"."creator_user_id" IS '操作的使用者 ID';

