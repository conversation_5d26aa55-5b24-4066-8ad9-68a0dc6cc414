import { PageTopic } from 'src/database/page_topic.entity';
import { User } from 'src/database/user.entity';

import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';

export enum PageStatusEnum {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  UNPUBLISHED = 'UNPUBLISHED',
  ARCHIVED = 'ARCHIVED',
}

export enum PageTypeEnum {
  CASE_STUDY_MAIN = 'CASE_STUDY_MAIN',
  CASE_STUDY_LIST = 'CASE_STUDY_LIST',
  PRODUCT_MAIN = 'PRODUCT_MAIN',
  PRODUCT_LIST = 'PRODUCT_LIST',
  NEWS_MAIN = 'NEWS_MAIN',
  NEWS_LIST = 'NEWS_LIST',
  BLOG_MAIN = 'BLOG_MAIN',
  BLOG_LIST = 'BLOG_LIST',
  CONTACT_US = 'CONTACT_US',
}

export const PAGE_TABLE_NAME = 'page';
@Entity(PAGE_TABLE_NAME)
@Index('idx_unique_page_slug', ['slug'], {
  unique: true,
  where: 'deleted_at IS NULL',
})
export class Page {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    name: 'slug',
    type: 'varchar',
    length: 255,
  })
  slug: string;

  @Column({
    name: 'title',
    type: 'varchar',
    length: 500,
  })
  title: string;

  @Column({
    name: 'logo_url',
    type: 'text',
    nullable: true,
  })
  logoUrl?: string;

  @Column({
    name: 'meta_og_title',
    type: 'varchar',
    length: 60,
    nullable: true,
  })
  metaOgTitle?: string;

  @Column({
    name: 'meta_og_description',
    type: 'varchar',
    length: 160,
    nullable: true,
  })
  metaOgDescription?: string;

  @Column({
    name: 'meta_og_image',
    type: 'text',
    nullable: true,
  })
  metaOgImage?: string;

  @Column({
    name: 'meta_keywords',
    type: 'text',
    nullable: true,
  })
  metaKeywords?: string;

  @Column({
    name: 'page_type',
    type: 'varchar',
    length: 50,
  })
  pageType: PageTypeEnum;

  @Column({
    name: 'status',
    type: 'enum',
    enum: PageStatusEnum,
    default: PageStatusEnum.DRAFT,
  })
  status: PageStatusEnum;

  @Column({
    name: 'parent_page_id',
    type: 'integer',
    nullable: true,
  })
  parentPageId?: number;

  @ManyToOne(() => Page)
  @JoinColumn({ name: 'parent_page_id' })
  parentPage?: Page;

  @OneToMany(() => Page, (page) => page.parentPage)
  childPages?: Page[];

  @Column({
    name: 'publisher_user_id',
    type: 'integer',
    nullable: true,
  })
  publisherUserId: number;

  @Column({
    name: 'published_at',
    type: 'bigint',
    nullable: true,
  })
  publishedAt?: bigint;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'publisher_user_id' })
  publisher?: User;

  @Column({
    name: 'creator_user_id',
    type: 'integer',
  })
  creatorUserId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'creator_user_id' })
  creator?: User;

  @Column({
    name: 'updater_user_id',
    type: 'integer',
  })
  updaterUserId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updater_user_id' })
  updater?: User;

  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => 'TRUNC(EXTRACT(EPOCH from NOW()) * 1000)',
  })
  createdAt?: bigint;

  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => 'TRUNC(EXTRACT(EPOCH from NOW()) * 1000)',
  })
  updatedAt?: bigint;

  @Column({
    name: 'deleted_at',
    type: 'bigint',
    nullable: true,
  })
  deletedAt?: bigint;

  @Column({
    name: 'deleter_user_id',
    type: 'integer',
    nullable: true,
  })
  deleterUserId?: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'deleter_user_id' })
  deleter?: User;

  @OneToMany(() => PageTopic, (topicPage) => topicPage.page, {
    cascade: true,
    eager: false,
  })
  topicPages?: PageTopic[];
}
