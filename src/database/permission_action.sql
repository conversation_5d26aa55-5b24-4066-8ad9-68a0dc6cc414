CREATE TABLE permission_action (
  key           text PRIMARY KEY,
  name          text NOT NULL,
  permission_object_key   text NOT NULL,
  required_keys text[],
  description   text,
  position      integer NOT NULL
);


COMMENT ON COLUMN "permission_action"."key" IS '操作行為 Key';

COMMENT ON COLUMN "permission_action"."name" IS '操作行為名稱';

COMMENT ON COLUMN "permission_action"."permission_object_key" IS '操作行為所隸屬的功能 Key';

COMMENT ON COLUMN "permission_action"."required_keys" IS '相關聯的操作行為 Key';

COMMENT ON COLUMN "permission_action"."description" IS '操作行為描述';

COMMENT ON COLUMN "permission_action"."position" IS '操作行為於 UI 上的排序';