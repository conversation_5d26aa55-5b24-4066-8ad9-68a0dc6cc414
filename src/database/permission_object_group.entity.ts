import { Column, <PERSON><PERSON>ty, OneToMany, PrimaryColumn } from 'typeorm';
import { PermissionObject } from './permission_object.entity';

@Entity('permission_object_group')
export class PermissionObjectGroup {
  @PrimaryColumn({
    name: 'key',
    type: 'text',
  })
  key: string;

  @Column({
    name: 'name',
    type: 'text',
  })
  name: string;

  @Column({
    name: 'position',
    type: 'integer',
  })
  position: number;

  @OneToMany(() => PermissionObject, (object) => object.permissionObjectGroup)
  permissionObjects?: PermissionObject[];
}
