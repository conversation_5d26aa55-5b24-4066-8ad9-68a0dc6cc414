import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyTo<PERSON>ne,
  PrimaryGeneratedColumn,
  Unique,
} from 'typeorm';

import { User } from 'src/database/user.entity';
import { Page } from 'src/database/page.entity';
import { Topic } from 'src/database/topic.entity';

export const PAGE_TOPIC_TABLE_NAME = 'page_topic';
@Entity(PAGE_TOPIC_TABLE_NAME)
@Unique(['topicId', 'pageId', 'deletedAt'])
export class PageTopic {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    name: 'order',
    type: 'integer',
    nullable: true,
  })
  order?: number;

  @Column({
    name: 'topic_id',
    type: 'integer',
  })
  topicId: number;

  @ManyToOne(() => Topic, (topic) => topic.topicPages)
  @JoinColumn({ name: 'topic_id' })
  topic?: Topic;

  @Column({
    name: 'page_id',
    type: 'integer',
  })
  pageId: number;

  @ManyToOne(() => Page, (page) => page.topicPages)
  @JoinColumn({ name: 'page_id' })
  page?: Page;

  @Column({
    name: 'creator_user_id',
    type: 'integer',
  })
  creatorUserId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'creator_user_id' })
  creator?: User;

  @Column({
    name: 'updater_user_id',
    type: 'integer',
  })
  updaterUserId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updater_user_id' })
  updater?: User;

  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => 'TRUNC(EXTRACT(EPOCH from NOW()) * 1000)',
  })
  createdAt?: bigint;

  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => 'TRUNC(EXTRACT(EPOCH from NOW()) * 1000)',
  })
  updatedAt?: bigint;

  @Column({
    name: 'deleted_at',
    type: 'bigint',
    nullable: true,
  })
  deletedAt?: bigint;

  @Column({
    name: 'deleter_user_id',
    type: 'integer',
    nullable: true,
  })
  deleterUserId?: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'deleter_user_id' })
  deleter?: User;
}
