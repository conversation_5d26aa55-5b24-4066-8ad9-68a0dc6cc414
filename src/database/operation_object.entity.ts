import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
} from 'typeorm';
import { OperationObjectGroup } from './operation_object_group.entity';
import { Operation } from './operation.entity';

@Entity('operation_object')
export class OperationObject {
  @PrimaryColumn({
    name: 'key',
    type: 'text',
  })
  key: string;

  @Column({
    name: 'name',
    type: 'text',
  })
  name: string;

  @Column({
    name: 'position',
    type: 'integer',
  })
  position: number;

  @Column({
    name: 'operation_object_group_key',
    type: 'text',
  })
  operationObjectGroupKey: string;

  @ManyToOne(
    () => OperationObjectGroup,
    (operationObjectGroup) => operationObjectGroup.operationObjects,
  )
  @JoinColumn({
    name: 'operation_object_group_key',
  })
  operationObjectGroup?: OperationObjectGroup;

  @OneToMany(() => Operation, (operation) => operation.operationObject)
  operations?: Operation[];
}
