import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  Jo<PERSON><PERSON><PERSON>umn,
  <PERSON>ToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Operation } from './operation.entity';
import { OperationEvent } from './operation_event.entity';
import { User } from './user.entity';

export enum OperationLogTargetTypeEnum {
  ROLE = 'ROLE', // 角色
  USER = 'USER', // 使用者
  TERMS = 'TERMS', // 條款
}

@Entity('operation_log')
export class OperationLog {
  @PrimaryGeneratedColumn({
    name: 'id',
    type: 'bigint',
  })
  id: bigint;

  @Column({
    name: 'operation_key',
    type: 'text',
  })
  operationKey: string;

  @ManyToOne(() => Operation)
  @JoinColumn({
    name: 'operation_key',
  })
  operation?: Operation;

  @Column({
    name: 'operation_event_key',
    type: 'text',
    nullable: true,
  })
  operationEventKey?: string;

  @ManyToOne(() => OperationEvent)
  @JoinColumn({
    name: 'operation_event_key',
  })
  operationEvent?: OperationEvent;

  @Column({
    name: 'target_type',
    type: 'enum',
    enum: OperationLogTargetTypeEnum,
    nullable: true,
  })
  targetType?: OperationLogTargetTypeEnum;

  @Column({
    name: 'target_id',
    type: 'text',
    nullable: true,
  })
  targetId?: string;

  @Column({
    name: 'target_name',
    type: 'text',
    nullable: true,
  })
  targetName?: string;

  @Column({
    name: 'input_data',
    type: 'jsonb',
    nullable: true,
  })
  inputData?: Record<string, unknown>;

  @Column({
    name: 'remarks',
    type: 'text',
    array: true,
    nullable: true,
  })
  remarks?: string[];

  @Column({
    name: 'is_success',
    type: 'boolean',
  })
  isSuccess: boolean;

  @Column({
    name: 'target_original_data',
    type: 'jsonb',
    nullable: true,
  })
  targetOriginalData?: Record<string, unknown>;

  @Column({
    name: 'output_data',
    type: 'jsonb',
    nullable: true,
  })
  outputData?: Record<string, unknown>;

  @Column({
    name: 'fail_code',
    type: 'integer',
    nullable: true,
  })
  failCode?: number;

  @Column({
    name: 'fail_reason',
    type: 'text',
    nullable: true,
  })
  failReason?: string;

  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => 'TRUNC(EXTRACT(EPOCH from NOW()) * 1000)',
  })
  createdAt?: bigint;

  @Column({
    name: 'creator_user_id',
    type: 'integer',
  })
  creatorUserId: number;

  @ManyToOne(() => User)
  @JoinColumn({
    name: 'creator_user_id',
  })
  creator?: User;
}
