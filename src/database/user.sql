CREATE TYPE user_status AS ENUM ('UNVERIFIED', 'VERIFIED', 'SUSPENDED');

CREATE TABLE "user" (
    id                 SERIAL PRIMARY KEY,
    email              varchar(320) NOT NULL,
    first_name          text NOT NULL,
    last_name          text NOT NULL,
    department         text NOT NULL,
    title              text NOT NULL,
    status             user_status NOT NULL,
    password           text,
    set_password_token text,
    reset_password_token text,
    password_login_allowed boolean,
    creator_user_id    integer NOT NULL,
    updater_user_id    integer NOT NULL,
    created_at         bigint NOT NULL DEFAULT TRUNC(EXTRACT(EPOCH FROM NOW()) * 1000),
    updated_at         bigint NOT NULL DEFAULT TRUNC(EXTRACT(EPOCH FROM NOW()) * 1000),
    deleted_at         bigint,
    deleter_user_id    integer
);

CREATE UNIQUE INDEX unique_email_constraint ON "user" (email) WHERE deleted_at IS NULL;

COMMENT ON COLUMN "public"."user"."id" IS '使用者 ID';

COMMENT ON COLUMN "public"."user"."email" IS '使用者信箱';

COMMENT ON COLUMN "public"."user"."first_name" IS '使用者名字';

COMMENT ON COLUMN "public"."user"."last_name" IS '使用者姓氏';

COMMENT ON COLUMN "public"."user"."department" IS '使用者所屬的部門';

COMMENT ON COLUMN "public"."user"."title" IS '使用者職稱';

COMMENT ON COLUMN "public"."user"."status" IS '使用者狀態 (UNVERIFIED: 未驗證, VERIFIED: 已驗證, SUSPENDED: 停用)';

COMMENT ON COLUMN "public"."user"."created_at" IS '新增使用者的時間 (unix timestamp)';

COMMENT ON COLUMN "public"."user"."password" IS '使用者的密碼';

COMMENT ON COLUMN "public"."user"."set_password_token" IS '設定密碼的 token';

COMMENT ON COLUMN "public"."user"."reset_password_token" IS '重設密碼的 token';

COMMENT ON COLUMN "public"."user"."password_login_allowed" IS '允許使用密碼登入';

COMMENT ON COLUMN "public"."user"."creator_user_id" IS '新增使用者的使用者 ID';

COMMENT ON COLUMN "public"."user"."updated_at" IS '更新使用者的時間 (unix timestamp)';

COMMENT ON COLUMN "public"."user"."updater_user_id" IS '更新使用者的使用者 ID';

COMMENT ON COLUMN "public"."user"."deleted_at" IS '刪除使用者的時間 (unix timestamp)';

COMMENT ON COLUMN "public"."user"."deleter_user_id" IS '刪除使用者的使用者 ID';