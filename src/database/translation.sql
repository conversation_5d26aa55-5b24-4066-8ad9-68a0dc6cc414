CREATE TYPE translation_language_code_enum AS ENUM('en', 'zhTW');

CREATE TABLE IF NOT EXISTS
    translation (
        id SERIAL PRIMARY KEY,
        resource_id INTEGER NOT NULL,
        resource_table_name TEXT NOT NULL,
        resource_field_name TEXT NOT NULL,
        language_code translation_language_code_enum NOT NULL,
        value jsonb NOT NULL,
        creator_user_id INTEGER NOT NULL,
        updater_user_id INTEGER NOT NULL,
        created_at BIGINT NOT NULL DEFAULT TRUNC(
            (
                EXTRACT(
                    epoch
                    FROM
                        NOW()
                ) * (1000)::NUMERIC
            )
        ),
        updated_at BIGINT NOT NULL DEFAULT TRUNC(
            (
                EXTRACT(
                    epoch
                    FROM
                        NOW()
                ) * (1000)::NUMERIC
            )
        ),
        deleted_at BIGINT,
        deleter_user_id INTEGER,
        unique_value VARCHAR
    );

CREATE UNIQUE INDEX "idx_unique_translation" ON translation USING btree (
    resource_id,
    resource_table_name,
    resource_field_name,
    language_code,
    deleted_at
);

CREATE UNIQUE INDEX "idx_unique_translation_unique_value" ON translation USING btree (
    unique_value,
    resource_table_name,
    resource_field_name,
    deleted_at
) WHERE unique_value IS NOT NULL;