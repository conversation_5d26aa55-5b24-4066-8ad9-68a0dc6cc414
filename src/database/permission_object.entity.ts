import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
} from 'typeorm';
import { PermissionAction } from './permission_action.entity';
import { PermissionObjectGroup } from './permission_object_group.entity';

@Entity('permission_object')
export class PermissionObject {
  @PrimaryColumn({
    name: 'key',
    type: 'text',
  })
  key: string;

  @Column({
    name: 'name',
    type: 'text',
  })
  name: string;

  @Column({
    name: 'permission_object_group_key',
    type: 'text',
  })
  permissionObjectGroupKey: string;

  @Column({
    name: 'description',
    type: 'text',
    nullable: true,
  })
  description?: string;

  @Column({
    name: 'position',
    type: 'integer',
  })
  position: number;

  @ManyToOne(() => PermissionObjectGroup, (group) => group.permissionObjects)
  @JoinColumn({ name: 'permission_object_group_key' })
  permissionObjectGroup?: PermissionObjectGroup;

  @OneToMany(() => PermissionAction, (action) => action.permissionObject)
  permissionActions?: PermissionAction[];
}
