CREATE TABLE IF NOT EXISTS
    page_topic (
        id SERIAL PRIMARY KEY,
        topic_id INTEGER NOT NULL,
        page_id INTEGER NOT NULL,
        creator_user_id INTEGER NOT NULL,
        updater_user_id INTEGER NOT NULL,
        created_at BIGINT NOT NULL DEFAULT TRUNC(
            (
                EXTRACT(
                    epoch
                    FROM
                        NOW()
                ) * (1000)::NUMERIC
            )
        ),
        updated_at BIGINT NOT NULL DEFAULT TRUNC(
            (
                EXTRACT(
                    epoch
                    FROM
                        NOW()
                ) * (1000)::NUMERIC
            )
        ),
        deleted_at BIGINT,
        deleter_user_id INTEGER,
        order INTEGER
    );

CREATE UNIQUE INDEX "idx_unique_page_topic" ON page_topic USING btree (topic_id, page_id, deleted_at);
