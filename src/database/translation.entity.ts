import {
  Column,
  <PERSON><PERSON><PERSON>,
  <PERSON>in<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
  Unique,
} from 'typeorm';

import { User } from 'src/database/user.entity';
import { Page } from 'src/database/page.entity';

export enum LanguageCodeEnum {
  EN = 'en',
  ZHTW = 'zhTW',
}

export const TRANSLATION_TABLE_NAME = 'translation';
@Entity(TRANSLATION_TABLE_NAME)
@Unique([
  'resourceId',
  'resourceTableName',
  'resourceFieldName',
  'languageCode',
  'deletedAt',
])
export class Translation {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    name: 'resource_id',
    type: 'integer',
  })
  resourceId: number;

  @Column({
    name: 'resource_table_name',
    type: 'text',
  })
  resourceTableName: string;

  @Column({
    name: 'resource_field_name',
    type: 'text',
  })
  resourceFieldName: string;

  @Column({
    name: 'language_code',
    type: 'enum',
    enum: LanguageCodeEnum,
  })
  languageCode: LanguageCodeEnum;

  @Column({
    name: 'value',
    type: 'jsonb',
  })
  value: any;

  @Column({
    name: 'creator_user_id',
    type: 'integer',
  })
  creatorUserId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'creator_user_id' })
  creator?: User;

  @Column({
    name: 'updater_user_id',
    type: 'integer',
  })
  updaterUserId: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updater_user_id' })
  updater?: User;

  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => 'TRUNC(EXTRACT(EPOCH from NOW()) * 1000)',
  })
  createdAt?: bigint;

  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => 'TRUNC(EXTRACT(EPOCH from NOW()) * 1000)',
  })
  updatedAt?: bigint;

  @Column({
    name: 'deleted_at',
    type: 'bigint',
    nullable: true,
  })
  deletedAt?: bigint;

  @Column({
    name: 'deleter_user_id',
    type: 'integer',
    nullable: true,
  })
  deleterUserId?: number;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'deleter_user_id' })
  deleter?: User;

  @Column({
    name: 'unique_value',
    type: 'varchar',
  })
  uniqueValue?: string;

  @ManyToOne(() => Page)
  @JoinColumn({
    name: 'resource_id',
    foreignKeyConstraintName: 'fk_translation_page',
  })
  page?: Page;
}
