import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>n,
  ManyToOne,
  OneToMany,
  PrimaryColumn,
} from 'typeorm';
import { OperationObject } from './operation_object.entity';
import { OperationEvent } from './operation_event.entity';

@Entity('operation')
export class Operation {
  @PrimaryColumn({
    name: 'key',
    type: 'text',
  })
  key: string;

  @Column({
    name: 'name',
    type: 'text',
  })
  name: string;

  @Column({
    type: 'integer',
  })
  position: number;

  @Column({
    name: 'operation_object_key',
    type: 'text',
  })
  operationObjectKey: string;

  @ManyToOne(
    () => OperationObject,
    (operationObject) => operationObject.operations,
  )
  @JoinColumn({
    name: 'operation_object_key',
  })
  operationObject?: OperationObject;

  @OneToMany(() => OperationEvent, (event) => event.operation)
  operationEvents?: OperationEvent[];
}
