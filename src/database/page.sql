CREATE TYPE page_status_enum AS ENUM('DRAFT', 'PUBLISHED', 'UNPUBLISHED', 'ARCHIVED');

CREATE TABLE IF NOT EXISTS
    page (
        id SERIAL PRIMARY KEY,
        slug CHARACTER VARYING(255) NOT NULL,
        title CHARACTER VARYING(500) NOT NULL,
        logo_url TEXT,
        meta_og_title CHARACTER VARYING(60),
        meta_og_description CHARACTER VARYING(160),
        meta_og_image TEXT,
        meta_keywords TEXT,
        page_type CHARACTER VARYING(50) NOT NULL,
        status page_status_enum NOT NULL DEFAULT 'DRAFT'::page_status_enum,
        parent_page_id INTEGER,
        publisher_user_id INTEGER,
        published_at BIGINT,
        creator_user_id INTEGER NOT NULL,
        updater_user_id INTEGER NOT NULL,
        created_at BIGINT NOT NULL DEFAULT TRUNC(
            (
                EXTRACT(
                    epoch
                    FROM
                        NOW()
                ) * (1000)::NUMERIC
            )
        ),
        updated_at BIGINT NOT NULL DEFAULT TRUNC(
            (
                EXTRACT(
                    epoch
                    FROM
                        NOW()
                ) * (1000)::NUMERIC
            )
        ),
        deleted_at BIGINT,
        deleter_user_id INTEGER
    );

CREATE UNIQUE INDEX "idx_unique_page_slug" ON page USING btree (slug, deleted_at);