import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import { PermissionObject } from './permission_object.entity';

@Entity('permission_action')
export class PermissionAction {
  @PrimaryColumn({
    name: 'key',
    type: 'text',
  })
  key: string;

  @Column({
    name: 'name',
    type: 'text',
  })
  name: string;

  @Column({
    name: 'permission_object_key',
    type: 'text',
  })
  permissionObjectKey: string;

  @Column({
    name: 'required_keys',
    type: 'text',
    array: true,
    nullable: true,
  })
  requiredKeys?: string[];

  @Column({
    name: 'description',
    type: 'text',
    nullable: true,
  })
  description?: string;

  @Column({
    name: 'position',
    type: 'integer',
  })
  position: number;

  @ManyToOne(() => PermissionObject, (object) => object.permissionActions)
  @JoinColumn({ name: 'permission_object_key' })
  permissionObject?: PermissionObject;
}
