import { ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { ConfigService } from '@nestjs/config';
import { ResponseInterceptor } from './interceptors/response/response.interceptor';
import {
  NestFastifyApplication,
  FastifyAdapter,
} from '@nestjs/platform-fastify';

async function bootstrap() {
  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    // TODO: 待補充 logger 記錄內容（因升級 nestjs 相關套件，舊版寫法不適用、討論 logger 記錄內容）
    new FastifyAdapter({ logger: true }),
  );

  const configService = app.get(ConfigService);
  // NOTE: API 全域前綴配置
  app.setGlobalPrefix(configService.get<string>('appGlobalPrefix'));

  // NOTE: CORS 配置
  app.enableCors({
    origin: configService.get<string>('appCorsAllowOrigin'),
    methods: configService.get<string>('appCorsAllowMethods').split(','),
  });

  // NOTE: Swagger 文件配置 (prod 環境不啟用 Swagger)
  if (configService.get<string>('appEnv') !== 'prod') {
    const swaggerDocumentOptions = new DocumentBuilder()
      .setTitle(configService.get<string>('swaggerDocumentTitle'))
      .setDescription(configService.get<string>('swaggerDocumentDescription'))
      .setVersion(configService.get<string>('swaggerDocumentVersion'))
      .addBearerAuth()
      .addBearerAuth(
        {
          type: 'http',
        },
        'oktaToken',
      )
      .build();
    const swaggerDocument = SwaggerModule.createDocument(
      app,
      swaggerDocumentOptions,
    );
    SwaggerModule.setup(
      configService.get<string>('swaggerDocumentPath'),
      app,
      swaggerDocument,
    );
  }

  // NOTE: 全域回傳配置
  app.useGlobalInterceptors(new ResponseInterceptor());
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
    }),
  );

  await app.listen(
    configService.get<number>('appPort'),
    configService.get<string>('appAddress'),
  );
}
bootstrap();
