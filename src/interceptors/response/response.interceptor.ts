import {
  CallH<PERSON><PERSON>,
  ExecutionContext,
  HttpException,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { isArray } from 'lodash';
import { Observable } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import {
  ApiResponseStatusEnum,
  LogResponseStatusEnum,
  ResponseInterface,
} from './response.interface';

@Injectable()
export class ResponseInterceptor<T>
  implements NestInterceptor<T, ResponseInterface<T>>
{
  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<ResponseInterface<T>> {
    return next.handle().pipe(
      map((data) => {
        const response: ResponseInterface<T> = {
          code: context.switchToHttp().getResponse().statusCode, // TODO: 目前沒有定義 code，code 暫時沿用 statusCode
          status: ApiResponseStatusEnum.SUCCESS,
          data: data?.operationLogResult ? data.result : data,
        };

        if (data?.operationLogResult) {
          const { isSuccess } = data?.operationLogResult;
          response.logStatus = isSuccess
            ? LogResponseStatusEnum.SUCCESS
            : LogResponseStatusEnum.FAILED;
        }

        return response;
      }),
      catchError(async (error) => {
        const statusCode = error.status;
        const response: Record<string, any> = {
          code: statusCode, // TODO: 目前沒有定義 code，code 暫時沿用 statusCode
          status: ApiResponseStatusEnum.FAILED,
        };

        if (error.response?.operationLogResult) {
          const { isSuccess } = error.response?.operationLogResult;
          response.logStatus = isSuccess
            ? LogResponseStatusEnum.SUCCESS
            : LogResponseStatusEnum.FAILED;
        }

        if (error.response?.message) {
          response.message = isArray(error.response.message)
            ? error.response.message.join('\n')
            : error.response.message;
        } else {
          response.message = error.message;
        }

        throw new HttpException(response, statusCode);
      }),
    );
  }
}
