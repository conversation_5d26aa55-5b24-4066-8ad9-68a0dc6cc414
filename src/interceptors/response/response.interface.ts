import { CreateOperationLogRes } from '../../modules/operation-logs/operation-logs.interface';

export enum ApiResponseStatusEnum {
  SUCCESS = 'success',
  FAILED = 'failed',
}

export enum LogResponseStatusEnum {
  SUCCESS = 'success',
  FAILED = 'failed',
}

export interface ApiResultAndCreateOperationLogResult<T> {
  result?: T; // MEMO: 新增與更新不一定會有資訊
  operationLogResult: CreateOperationLogRes;
}

export interface ResponseInterface<T> {
  code: number;
  logStatus?: LogResponseStatusEnum;
  status: ApiResponseStatusEnum;
  data?: T; // MEMO: 新增與更新不一定會有資訊
}
