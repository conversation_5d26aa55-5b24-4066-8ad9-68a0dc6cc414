import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { Request } from 'express';
import { ApiMetadataService } from 'src/decorators/api-metadata/api-metadata.service';
import {
  API_METADATA_KEY,
  ApiMetadataHeaderEnum,
} from 'src/decorators/api-metadata/api-metadata.interface';
@Injectable()
export class ApiMetadataInterceptor implements NestInterceptor {
  constructor(private readonly apiMetadataService: ApiMetadataService) {}
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const rawHeaders = this.apiMetadataService.extractRawHeaders(
      request.headers,
    );
    const processedMetadata =
      this.apiMetadataService.processHeaders(rawHeaders);
    request[API_METADATA_KEY] = processedMetadata;
    const response = context.switchToHttp().getResponse();
    response.headers({
      [ApiMetadataHeaderEnum.REQUEST_ID]: processedMetadata.requestId,
    });
    return next.handle();
  }
}
