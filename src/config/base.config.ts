export default () => ({
  appEnv: process.env.APP_ENV || 'dev', // MEMO: 依據環境填入 dev, test, stg, prod
  appPort: process.env.APP_PORT || 3000,
  appAddress: process.env.APP_ADDRESS || '0.0.0.0',
  appGlobalPrefix: process.env.APP_GLOBAL_PREFIX || 'api',
  appCorsAllowOrigin: process.env.APP_CORS_ALLOW_ORIGIN || '*', // MEMO: Prod 環境需要設置
  appCorsAllowMethods:
    process.env.APP_CORS_ALLOW_METHODS || 'GET,POST,PATCH,PUT,DELETE',
  swaggerDocumentTitle:
    process.env.SWAGGER_DOCUMENT_TITLE || 'Swagger Document Title',
  swaggerDocumentDescription:
    process.env.SWAGGER_DOCUMENT_DESCRIPTION || 'Swagger Document Description',
  swaggerDocumentVersion:
    process.env.SWAGGER_DOCUMENT_VERSION || 'Swagger Document Version',
  swaggerDocumentPath: process.env.SWAGGER_DOCUMENT_PATH || '/api/docs',
});
