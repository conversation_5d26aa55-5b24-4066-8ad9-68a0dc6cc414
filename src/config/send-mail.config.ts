export default () => ({
  sendMail: {
    logoUrl: process.env.SEND_MAIL_LOGO_URL,
    apiKey: process.env.SEND_MAIL_API_KEY,
    sanderEmail: process.env.SEND_MAIL_SENDER_EMAIL,
    sanderName: process.env.SEND_MAIL_SENDER_NAME,
    setPassword: {
      pageUrl: process.env.SEND_MAIL_SET_PASSWORD_PAGE_URL,
      tokenSecret: process.env.SEND_MAIL_SET_PASSWORD_TOKEN_SECRET,
      tokenExpiresIn: process.env.SEND_MAIL_SET_PASSWORD_TOKEN_EXPIRES_IN,
    },
    resetPassword: {
      pageUrl: process.env.SEND_MAIL_RESET_PASSWORD_PAGE_URL,
      tokenSecret: process.env.SEND_MAIL_RESET_PASSWORD_TOKEN_SECRET,
      tokenExpiresIn: process.env.SE<PERSON>_MAIL_RESET_PASSWORD_TOKEN_EXPIRES_IN,
    },
  },
});
