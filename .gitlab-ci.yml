variables:
  GROUP: platform
  # the project refers to some file hosted in another repo
  # use recursive strategy to fetch
  GIT_SUBMODULE_STRATEGY: recursive
  IMAGE_TAG: $CI_COMMIT_SHORT_SHA.$CI_PIPELINE_IID

stages:
  - build_image
  - build-importer-image
  - update-version
  - update-importer-version
  - ai-code-review
  - check_commit_changes
  - fortify_sca

build_image:
  stage: build_image
  image:
    name: gcr.io/kaniko-project/executor:v1.15.0-debug
    entrypoint: ['']
  rules:
    - if: $CI_COMMIT_TAG =~ /^[0-9]+\.[0-9]+\.[0-9]+$/ || $CI_COMMIT_TAG =~ /(DEV|TEST|STG)$/
    - if: '$CI_COMMIT_BRANCH == "dev"'
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  tags:
    - shared-runners
  variables:
    IMAGE_NAME: ${GROUP}/${CI_PROJECT_NAME}
    NODE_ENV: stg
    TARGET_ENV: stg
  script:
    ### Specify auth config to kaniko
    - mkdir -p /kaniko/.docker
    - echo ${CI_PROJECT_DIR}
    - echo "{\"credsStore\":\"ecr-login\"}" > /kaniko/.docker/config.json
    # Build and push image to multiple registries
    - /kaniko/executor
      --build-arg CI_PROJECT_DIR=${CI_PROJECT_DIR}
      --build-arg NODE_ENV=${NODE_ENV}
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/Dockerfile"
      --destination "${AWS_REGISTRY_NON_PROD}/${IMAGE_NAME}:${IMAGE_TAG}"
      --destination "${AWS_REGISTRY_PROD}/${IMAGE_NAME}:${IMAGE_TAG}"

# .build-importer-image:
#   stage: build-importer-image
#   image:
#     name: gcr.io/kaniko-project/executor:v1.15.0-debug
#     entrypoint: ['']
#   tags:
#     - shared-runners
#   variables:
#     IMAGE_NAME: ${GROUP}/${CI_PROJECT_NAME}
#   script:
#     - mkdir -p /kaniko/.docker
#     - echo ${CI_PROJECT_DIR}
#     - echo "{\"credsStore\":\"ecr-login\"}" > /kaniko/.docker/config.json
#     # Build and push image to multiple registries
#     - /kaniko/executor
#       --build-arg CI_PROJECT_DIR=${CI_PROJECT_DIR}
#       --build-arg NODE_ENV=${NODE_ENV}
#       --context "${CI_PROJECT_DIR}"
#       --dockerfile "${CI_PROJECT_DIR}/Dockerfile"
#       --destination "${CI_REGISTRY_IMAGE}:${IMAGE_TAG}"
#       --destination "${AWS_REGISTRY_VAR}/${IMAGE_NAME}:${IMAGE_TAG}"

# build-importer-image:dev:
#   extends: .build-importer-image
#   rules:
#     - if: $CI_COMMIT_TAG =~ /update_db_dev/
#   variables:
#     TARGET_ENV: dev
#     NODE_ENV: dev
#     AWS_REGISTRY_VAR: ${AWS_REGISTRY_NON_PROD}

# build-importer-image:test:
#   extends: .build-importer-image
#   rules:
#     - if: $CI_COMMIT_TAG =~ /update_db_test/
#   variables:
#     TARGET_ENV: dev
#     NODE_ENV: test
#     AWS_REGISTRY_VAR: ${AWS_REGISTRY_NON_PROD}

# build-importer-image:stg:
#   extends: .build-importer-image
#   rules:
#     - if: $CI_COMMIT_TAG =~ /update_db_stg/
#   variables:
#     TARGET_ENV: stg
#     NODE_ENV: stg
#     AWS_REGISTRY_VAR: ${AWS_REGISTRY_NON_PROD}

# build-importer-image:prod:
#   extends: .build-importer-image
#   rules:
#     - if: $CI_COMMIT_TAG =~ /update_db_prod/
#   variables:
#     TARGET_ENV: prod
#     NODE_ENV: prod
#     AWS_REGISTRY_VAR: ${AWS_REGISTRY_PROD}

update-version:dev:
  stage: update-version
  allow_failure: false
  inherit:
    variables: true
  needs: ['build_image']
  variables:
    UPSTREAM_PROJECT_NAME: $CI_PROJECT_NAME
    UPSTREAM_GITLAB_USER_EMAIL: $GITLAB_USER_EMAIL
    UPSTREAM_GITLAB_USER_NAME: $GITLAB_USER_NAME
    UPSTREAM_CI_JOB_NAME: $CI_JOB_NAME
    TARGET_ENV: dev
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
    - if: $CI_COMMIT_TAG =~ /DEV/
    - if: '$CI_COMMIT_BRANCH == "dev"'
      changes:
        - '**/*'
  trigger:
    project: vyin/manifest/vyin-ai-web-api-backend-deploy
    branch: main
    strategy: depend

update-version:stg:
  stage: update-version
  allow_failure: false
  inherit:
    variables: true
  needs: ['build_image']
  variables:
    UPSTREAM_PROJECT_NAME: $CI_PROJECT_NAME
    UPSTREAM_GITLAB_USER_EMAIL: $GITLAB_USER_EMAIL
    UPSTREAM_GITLAB_USER_NAME: $GITLAB_USER_NAME
    UPSTREAM_CI_JOB_NAME: $CI_JOB_NAME
    TARGET_ENV: stg
  rules:
    - if: '$CI_COMMIT_TAG =~ /STG/'
  trigger:
    project: vyin/manifest/vyin-ai-web-api-backend-deploy
    branch: main
    strategy: depend

update-version:test:
  stage: update-version
  allow_failure: false
  inherit:
    variables: true
  needs: ['build_image']
  variables:
    UPSTREAM_PROJECT_NAME: $CI_PROJECT_NAME
    UPSTREAM_GITLAB_USER_EMAIL: $GITLAB_USER_EMAIL
    UPSTREAM_GITLAB_USER_NAME: $GITLAB_USER_NAME
    UPSTREAM_CI_JOB_NAME: $CI_JOB_NAME
    TARGET_ENV: test
  rules:
    - if: '$CI_COMMIT_TAG =~ /TEST/'
  trigger:
    project: vyin/manifest/vyin-ai-web-api-backend-deploy
    branch: main
    strategy: depend

update-version:prod:
  stage: update-version
  allow_failure: false
  inherit:
    variables: true
  needs: ['build_image']
  variables:
    UPSTREAM_PROJECT_NAME: $CI_PROJECT_NAME
    UPSTREAM_GITLAB_USER_EMAIL: $GITLAB_USER_EMAIL
    UPSTREAM_GITLAB_USER_NAME: $GITLAB_USER_NAME
    UPSTREAM_CI_JOB_NAME: $CI_JOB_NAME
    TARGET_ENV: prod
  rules:
    - if: $CI_COMMIT_TAG =~ /^[0-9]+\.[0-9]+\.[0-9]+$/
  trigger:
    project: vyin/manifest/vyin-ai-web-api-backend-deploy
    branch: main
    strategy: depend

# update-importer-version:dev:
#   stage: update-importer-version
#   allow_failure: false
#   inherit:
#     variables: true
#   needs: ['build-importer-image:dev']
#   variables:
#     UPSTREAM_CI_JOB_NAME: $CI_JOB_NAME
#     UPSTREAM_PROJECT_NAME: $CI_PROJECT_NAME
#     UPSTREAM_GITLAB_USER_EMAIL: $GITLAB_USER_EMAIL
#     UPSTREAM_GITLAB_USER_NAME: $GITLAB_USER_NAME
#     TARGET_ENV: dev
#     TARGET_IMAGE_TAG_PATH: '.importer.image.tag'
#   rules:
#     - if: $CI_COMMIT_TAG =~ /update_db_dev/
#   trigger:
#     project: beanfun/intl/manifest/viefor-backstage-web-backend-deploy
#     branch: main
#     strategy: depend

# update-importer-version:test:
#   stage: update-importer-version
#   allow_failure: false
#   inherit:
#     variables: true
#   needs: ['build-importer-image:test']
#   variables:
#     UPSTREAM_CI_JOB_NAME: $CI_JOB_NAME
#     UPSTREAM_PROJECT_NAME: $CI_PROJECT_NAME
#     UPSTREAM_GITLAB_USER_EMAIL: $GITLAB_USER_EMAIL
#     UPSTREAM_GITLAB_USER_NAME: $GITLAB_USER_NAME
#     TARGET_ENV: test
#     TARGET_IMAGE_TAG_PATH: '.importer.image.tag'
#   rules:
#     - if: $CI_COMMIT_TAG =~ /update_db_test/
#   trigger:
#     project: beanfun/intl/manifest/viefor-backstage-web-backend-deploy
#     branch: main
#     strategy: depend

# update-importer-version:stg:
#   stage: update-importer-version
#   allow_failure: false
#   inherit:
#     variables: true
#   needs: ['build-importer-image:stg']
#   variables:
#     UPSTREAM_CI_JOB_NAME: $CI_JOB_NAME
#     UPSTREAM_PROJECT_NAME: $CI_PROJECT_NAME
#     UPSTREAM_GITLAB_USER_EMAIL: $GITLAB_USER_EMAIL
#     UPSTREAM_GITLAB_USER_NAME: $GITLAB_USER_NAME
#     TARGET_ENV: stg
#     TARGET_IMAGE_TAG_PATH: '.importer.image.tag'
#   rules:
#     - if: $CI_COMMIT_TAG =~ /update_db_stg/
#   trigger:
#     project: beanfun/intl/manifest/viefor-backstage-web-backend-deploy
#     branch: main
#     strategy: depend

# update-importer-version:prod:
#   stage: update-importer-version
#   allow_failure: false
#   inherit:
#     variables: true
#   needs: ['build-importer-image:prod']
#   variables:
#     UPSTREAM_CI_JOB_NAME: $CI_JOB_NAME
#     UPSTREAM_PROJECT_NAME: $CI_PROJECT_NAME
#     UPSTREAM_GITLAB_USER_EMAIL: $GITLAB_USER_EMAIL
#     UPSTREAM_GITLAB_USER_NAME: $GITLAB_USER_NAME
#     TARGET_ENV: prod
#     TARGET_IMAGE_TAG_PATH: '.importer.image.tag'
#   rules:
#     - if: $CI_COMMIT_TAG =~ /update_db_prod/
#   trigger:
#     project: beanfun/intl/manifest/viefor-backstage-web-backend-deploy
#     branch: main
#     strategy: depend

ai-code-review:
  stage: ai-code-review
  image:
    name: codiumai/pr-agent:0.12
    entrypoint: ['']
  allow_failure: true
  tags:
    - shared-runners-no-root
  before_script:
    - cat ${CODIUMAI_SECRET_CONFIG} > /app/pr_agent/settings/.secrets.toml
    - cd /app
  script:
    - >
      python pr_agent/cli.py 
      --pr_url="${CI_MERGE_REQUEST_PROJECT_URL}/-/merge_requests/${CI_MERGE_REQUEST_IID}"
      review
      --config.git_provider="gitlab"
      --gitlab.url=${CI_SERVER_URL}
  only:
    - merge_requests

check_commit_changes:
  stage: check_commit_changes
  rules:
    - if: $CI_COMMIT_TAG =~ /(fortify)$/ || $CI_PIPELINE_SOURCE == "schedule"
  tags:
    - shared-runners-no-root
  script:
    - echo "Checking for changes..."
    - apt-get update -yqq && apt-get install git -yqq # install git
    - echo "------印出 15 天內的 commit 紀錄------"
    - echo "$(git log --since="15 days ago" --oneline)"
    - |
      if [ -z "$(git log --since="15 days ago" --oneline)" ]; then
        echo "------15 天內沒有 commit 紀錄，故拋出錯誤，結束流程，不執行 fortify_sca job------"
        exit 1
      fi
    - echo "Changes found."

fortify_sca:
  stage: fortify_sca
  needs:
    - check_commit_changes
  variables:
    TEMP_PATH: C:\GitLab-Runner
    BUILD_ID: $CI_PROJECT_NAME
    FILE_NAME: $CI_PROJECT_NAME
    APPLICATION: $CI_PROJECT_NAME
    VERSION: ${CI_COMMIT_TAG}
  rules:
    - if: $CI_COMMIT_TAG =~ /(fortify)$/ || $CI_PIPELINE_SOURCE == "schedule"
  script:
    - $APPLICATION = $APPLICATION.replace('/', '-')
    - $VERSION = $VERSION.replace('-', '')
    - echo $APPLICATION
    - update_rule_pack.bat
    - echo "CI_PROJECT_ROOT_NAMESPACE = $CI_PROJECT_ROOT_NAMESPACE"
    - echo "This job runs in the codereview stage."
    - echo "ci project path = $CI_PROJECT_DIR"
    - sourceanalyzer -b ${BUILD_ID} -clean
    - sourceanalyzer -b ${BUILD_ID} ${CI_PROJECT_DIR}\**\* -Dcom.fortify.sca.EnableDOMModeling=true
    - sourceanalyzer -b ${BUILD_ID} -scan -f "${FILE_NAME}.fpr"
    - python C:\Fortify_tool\fpr2ssc_v1_1.py -n ${APPLICATION}-SCA -v ${VERSION} -a "${FILE_NAME}.fpr"
    - Start-Sleep -Seconds 300
    - ReportGenerator -source "${FILE_NAME}.fpr.fpr" -template C:\Fortify_tool\DGC_Developer_Workbook_2022_en.xml -format xml -f "${FILE_NAME}.xml"
    # upload pdf to gitlab
    - ReportGenerator -source "${FILE_NAME}.fpr.fpr" -template C:\Fortify_tool\DGC_Developer_Workbook_2022_en.xml -format pdf -f "${FILE_NAME}-${CI_COMMIT_SHA}.pdf"

    - C:\Fortify_tool\parser1.ps1 "${FILE_NAME}.xml"
  artifacts:
    expire_in: 1 year
    name: '${FILE_NAME}-${CI_COMMIT_SHA}'
    when: always
    paths:
      - '**/${FILE_NAME}-${CI_COMMIT_SHA}.pdf'
  tags:
    - Fortify-Runner
