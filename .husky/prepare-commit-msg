#!/bin/bash

# exit the process for rebase, merge, and cherry-pick
if [[ "$GIT_REFLOG_ACTION" =~ "rebase" ]] || [[ "$GIT_REFLOG_ACTION" =~ "merge" ]] || [[ "$GIT_REFLOG_ACTION" =~ "cherry-pick" ]]; then
  exit 0
fi

if [ -d ".git/rebase-apply" ] || [ -d ".git/rebase-merge" ] || [ -f ".git/MERGE_HEAD" ] || [ -f ".git/CHERRY_PICK_HEAD" ]; then
  exit 0
fi

# get the current branch name
BRANCH_NAME=$(git rev-parse --abbrev-ref HEAD 2>/dev/null)

if [ "$BRANCH_NAME" = "HEAD" ] || [ -z "$BRANCH_NAME" ]; then
  exit 0
fi

# get the commit message
ORIGINAL_MSG="$(cat "$1")"

# extract ticket numbers from the branch name (supports multiple tickets in the format {English}-{Number})
BRANCH_TICKETS=$(echo "$BRANCH_NAME" | grep -oE "[A-Za-z]+-[0-9]+" | tr '\n' ' ' | sed 's/ $//')

# Exit the process if no valid ticket format is found in the branch name
if [ -z "$BRANCH_TICKETS" ]; then
  exit 0
fi

# Check if the commit message already contains a ticket number from the branch.
# If found, exit the process; otherwise, prepend the ticket numbers to the commit message.
for TICKET in $BRANCH_TICKETS; do
  if [[ "$ORIGINAL_MSG" =~ ^$TICKET[[:space:]] ]]; then
    exit 0 
  fi
done

echo "$BRANCH_TICKETS $ORIGINAL_MSG" > "$1"
