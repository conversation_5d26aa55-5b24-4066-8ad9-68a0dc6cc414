# beanfun-nestjs-backstage-template

## 簡介

- 語言: Typescript v5.4.3 (Node.js v20.18.2(npm v10.8.0))
- 框架: NestJs 10 ([Link](https://docs.nestjs.com/))
- 資料庫: PostgreSQL
- 相關套件
  - [@nestjs/config](https://www.npmjs.com/package/@nestjs/config): 取用環境參數
  - [class-validator](https://www.npmjs.com/package/class-validator): 欄位檢查用
  - [class-transformer](https://www.npmjs.com/package/class-transformer): 欄位型別轉換
  - [@nestjs/swagger](https://www.npmjs.com/package/@nestjs/swagger): 撰寫 API Swagger
  - [@okta/jwt-verifier](https://www.npmjs.com/package/@okta/jwt-verifier): 內部使用者登入(okta 驗證)
  - [@nestjs/jwt](https://www.npmjs.com/package/@nestjs/jwt)、[@nestjs/passport](https://www.npmjs.com/package/@nestjs/passport): 產生與驗證 JWT Token
  - [casbin](https://www.npmjs.com/package/casbin): 使用者權限檢查
  - [typeorm](https://www.npmjs.com/package/typeorm)、[@nestjs/typeorm](https://www.npmjs.com/package/@nestjs/typeorm): 定義資料表 entity 和操作資料表
  - [axios](https://www.npmjs.com/package/axios)、[@nestjs/axios](https://www.npmjs.com/package/@nestjs/axios): Call 外部服務 API
  - [dayjs](https://www.npmjs.com/package/dayjs): 時間轉換與處理

## 建立資料表與匯入資料

Step1. 建立資料表

```shell
cat src/database/*.sql | psql -h {database host} -d {database name} -f -
```

Step2. 打包程式碼（若無 `casbin_rule` table 在執行 `Step 4` 任一指令新建）

```shell
npm run build
```

Step3. 複製 `.env.sample` 檔名改為 `.env` 檔案，其中調整以下參數

```
DB_TYPE=postgres
DB_HOST=(database host)
DB_PORT=(database port)
DB_USER=(database user name)
DB_PASSWORD=(database user password)
DB_NAME=(database name)
```

Step4.匯入資料

P.S. 執行以下指令若發生錯誤會 retry 3 次，3 次失敗後會終止

4-1. 匯入權限相關資料與（若預設角色存在，同步更新預設角色的權限）

```shell
npm run import:permission-data
```

4-2. 匯入操作記錄相關資料

```shell
npm run import:operation-data
```

4-3. 新增預設角色並綁定權限（僅用於建置環境）

```shell
npm run import:admin-role
```

4-4. 新增 admin user 並綁定角色（僅用於建置環境）

```shell
npm run import:admin-user
```

## Notice

1. 此專案有使用 husky

- 在 ./husky/prepare-commit-msg 添加 commit 時，commit msg 會自動帶上 branch name，詳細邏輯請參考 prepare-commit-msg

- 在 ./husky/pre-push 添加 push 前會自動跑 npm run build:dev，有異常則會無法 push

## 開發與測試

1. 參考 `.env.sample`檔案以建立 `.env` 檔案

2. 啟動 Local API Server (debug 模式)

   ```shell
   npm run start:debug
   ```

3. 可以開啟 `http://localhost:3000/api/docs` 就可以看到 Swagger，進行 API 測試

## 開發注意事項

1.  命名規則

    - API endpoint naming: use pluralized nouns for resources

      ```
      Method: GET Path: /api/users (取得用戶列表)
      Method: GET Path: /api/users/{userId} (取得用戶資料)
      Method: PUT/PATCH Path: /api/users/{userId} (PUT: 更新用戶資料/PATCH:可部分更新用戶資料)
      Method: DELETE Path: /api/users/{userId} (刪除用戶資料)
      ```

    - variable naming: use lower camel case (命名風格請參考專案中的檔案)

    - table name and table field naming: use snake case

    - env: use upper snake case

2.  API request 需要在 `dto` 資料夾中定義好 API 所對應的 interface (包含 swagger 欄位說明、欄位檢查 (可參考`dto/xxx.dto.ts` 檔案)，並且使用 `class-validator` 套件做檢查)

3.  API response 需要在 `entities` 資料夾中定義好 API 所對應的 interface (包含 swagger 欄位說明) (可參考`entities/xxx.entity.ts` 檔案)

4.  善用 enum

    - 舉例: 用戶狀態有 `UNVERIFIED`, `VERIFIED`, `SUSPENDED` 三種，不使用 String 作為型別而是 enum

      ```ts
      export enum UserStatusEnum {
        UNVERIFIED = 'UNVERIFIED',
        VERIFIED = 'VERIFIED',
        SUSPENDED = 'SUSPENDED',
      }

        if (userStatus === UserStatusEnum.UNVERIFIED) { // O
        ...
      }

      if (userStatus === 'UNVERIFIED') { // X
        ....
      }
      ```

5.  時間有關的欄位

    - table 欄位有關於時間請使用 `bigint` 型別儲存，值皆為 unit timestamp (milliseconds)

    - API request, response 統一使用 `number` 型別

6.  每一個 function 需要明確定義傳入及回傳 interface，欄位型別盡量不要使用 any，也須需要定義欄位是否存在

    ```ts
    interface User {
      id: number;
      firstName: string;
      lastName: string;
      email: string;
      title?: number; // Optional
    }
    ```

7.  權限檢查規範

    - 開發步驟
      Step 1. 在 `permission-object-groups/permission-object-groups.interface.ts` 中新增權限資訊 (`permissionObjectGroup` 為功能分類，`permissionObject` 為功能，`permissionAction` 為功能操作行為)

      - 舉例: 取得使用者列表會對應到[權限管理>角色管理>檢視]

        ```ts
        export namespace AUTHORIZATION_MANAGEMENT {
          export const permissionObjectGroupKey = 'AUTHORIZATION_MANAGEMENT';
          export const permissionObjectGroupName = '權限管理';
          export const permissionObjectGroupPosition = 1;

          export namespace ROLE_MANAGEMENT {
            export const permissionObjectKey = `${permissionObjectGroupKey}_ROLE_MANAGEMENT`;
            export const permissionObjectName = '角色管理';
            export const permissionObjectDescription = '角色設定';
            export const permissionObjectPosition = 1;

            export const READ = {
              permissionObjectKey,
              permissionActionKey: `${permissionObjectKey}_READ`,
              permissionActionName: '檢視',
              permissionActionPosition: 1,
              permissionActionDescription:
                '檢視角色，包含列表、單一角色詳細資料',
            };
          }
        }
        ```

      Step 2. 在 `.service.ts` 檔案撰寫操作紀錄寫入的 function

      Step 3. 對應的 `.controller.ts` 檔案調用 `Step 2` 撰寫的 function

      - 舉例
        - `[GET] /api/roles API` 會在 `roles.controller.ts` 先調用 `roles.service.ts` 的 `checkAuthRoleReadPermission` 進行權限檢查，再接接著查詢角色列表
        - `[POST] /api/roles API` 會在 `roles.controller.ts` 先調用 `roles.service.ts` 的 `checkAuthRoleCreatePermission` 進行權限檢查，再接接著新增角色

    - 備註

      - 在 `permission-object-groups/permission-object-groups.interface.ts` 新增資訊除了程式碼調用外，執行資料匯入的時候，也會把相關資訊寫入至 DB 中

      - 預設每一支 API 皆需要檢查權限，不過多個功能共用的 API 不用進行權限檢查

8.  操作紀錄規範

    - 開發步驟

      Step 1. 在 `operation-object-groups/operation-object-groups.interface.ts` 中新增資訊 (`operationObjectGroup` 為功能分類，`operationObject` 為功能，`operation` 為操作項目/行為，`operationEvent` 為操作事件)

      - 舉例1: 取得使用者列表

        ```ts
        export namespace AUTHORIZATION_MANAGEMENT {
          export const operationObjectGroupKey = 'AUTHORIZATION_MANAGEMENT';
          export const operationObjectGroupName = '權限管理';
          export const operationObjectPosition = 1;
          export namespace USER_MANAGEMENT {
            export const operationObjectKey = `${operationObjectGroupKey}_USER_MANAGEMENT`;
            export const operationObjectName = '使用者設定';
            export const operationObjectPosition = 1;

            export const GET_USER_LIST = {
              operationObjectGroupKey,
              operationObjectKey,
              operationKey: `${operationObjectKey}_GET_USER_LIST`,
              operationName: '查詢使用者清單',
              operationPosition: 2,
              targetType: OperationLogTargetTypeEnum.USER,
            };
          }
        }
        ```

      - 舉例2: 編輯使用者資訊

        ```ts
        export namespace AUTHORIZATION_MANAGEMENT {
          export const operationObjectGroupKey = 'AUTHORIZATION_MANAGEMENT';
          export const operationObjectGroupName = '權限管理';
          export const operationObjectPosition = 1;
          export namespace USER_MANAGEMENT {
            export const operationObjectKey = `${operationObjectGroupKey}_USER_MANAGEMENT`;
            export const operationObjectName = '使用者設定';
            export const operationObjectPosition = 1;
            export namespace UPDATE_USER {
              const targetType = OperationLogTargetTypeEnum.USER;
              export const operationKey = `${operationObjectKey}_UPDATE_USER`;
              export const operationName = '編輯使用者';
              export const operationPosition = 4;

              export const UPDATE_INFO = {
                operationObjectGroupKey,
                operationObjectKey,
                operationKey,
                operationEventKey: `${operationKey}_UPDATE_INFO`,
                operationEventName: '更改資訊',
                operationEventPosition: 1,
                targetType,
              };
              // MEMO: other update event
            }
          }
        }
        ```

      Step 2. 在 `.service.ts` 檔案撰寫操作紀錄寫入的 function

      Step 3. 對應的 `.controller.ts` 檔案調用 `Step 2` 撰寫的 function

      - 舉例

        - `[GET] /api/roles API` 對應的 `roles.controller.ts` 在完成查詢列表後，會調用 `roles.service.ts` 的 `createRoleOperationLog` 將操作紀錄寫入 DB 中

        - `[POST] /api/roles API` 對應的 `roles.controller.ts` 新增完角色後，會調用 `roles.service.ts` 的 `createRoleOperationLog` 將操作紀錄寫入 DB 中

    - 備註

      - 在 `operation-object-groups/operation-object-groups.interface.ts` 新增資訊除了程式碼調用外，執行資料匯入的時候，也會把相關資訊寫入至 DB 中

      - `target_type` 是基於 resource 的

      - 預設每一支 API 皆需要紀錄操作，請注意以下特殊情境:

        - APP用戶管理、XXX管理皆會使用 `[GET] /api/app-users` API，僅需在APP用戶管理使用時才要紀錄，其餘不用紀錄

        - 角色管理中使用到的 `[GET] /api/permission-object-groups` API，因為是輔助角色管理功能，所以不用紀錄操作

9.  請安裝 ESlint 與 Prettier eslint 可檢查 code 顯而易見的錯誤及風格問題。而 prettier 幫助開發者能自動排版，確保 code 風格是一致的。另外，請在功能開發完畢 (發 MR 前)，執行以下指令

    ```shell
    npm run format
    ```

10. 部署/上線前需留意以下參數，以避免資安問題，可參考[前後端資安風險](https://gamania-group.atlassian.net/wiki/spaces/BP/pages/672366827)

- `APP_ENV`: 當設置為 `prod` 時，不會顯示 Swagger，避免測試文件外流
- `APP_CORS_ALLOW_ORIGIN`: 避免設定 `*`，應設定前端 domain
- `APP_CORS_ALLOW_METHODS`: 避免設定 `*`，應設定後端 API 有使用到的 Method

11. Comments in the code type

    - `MEMO:`: 筆記、便條紙的概念 (主要是寫一些提醒自己/其他人的事項)

    - `NOTE:`: 基本上同上，差異在於 NOTE 可能稍微強烈些，要自己/其他人注意的事項

    - `TODO:`: 待辦事項、待優化

    - `SPEC:`: 業務邏輯

    - `FIXME:`: 待修正

    - `PENDING:`: 功能尚未實作 or 釋出

12. Git Commit

    - Commit 類型

      - `feat`: 新增或修改功能

      - `fix`: 修正功能

      - `refactor`: 重構，例如：降低架構複雜度、抽取共用元件

      - `perf`: 效能優化，例如：優化用戶體驗、加速後端處理

      - `test`: 新增、修改、刪除測試程式

      - `docs`: 新增、修改、刪除註解、文件說明

      - `style`: 調整 style (不影響程式碼運行的變動 white-space, formatting, missing semi colons...)

      - `chore`: 非上述改動，例如： CI/CD 設定

    - Commit 格式

      ```
      /^(revert: )?(feat|fix|refactor|perf|test|chore): .{1,50}/
      ```

    - Commit 範例

      ```
      feat: add chat room component
      ```

## 資料夾結構說明

```
.
├── README.md
├── nest-cli.json
├── package-lock.json
├── package.json
├── src
│   ├── app.module.ts // import 各個 module (ex: import user module...)
│   ├── commands // 匯入資料到 Table 的工具
│   │   ├── console.ts
│   │   ├── import.console.ts
│   │   ├── import.module.ts
│   │   ├── import.service.ts
│   │   └── seeds
│   │       ├── admin-role.seed.ts
│   │       └── admin-user.seed.ts
│   ├── config // custom configuration files (https://docs.nestjs.com/techniques/configuration)
│   │   ├── auth.config.ts
│   │   └── ...
│   ├── database // 與資料庫操作有關 (1).sql: 用來記錄建立 table (or type) 語法 (2).ts: 用 TypeScript 定義資料表結構
│   │   ├── operation.entity.ts
│   │   ├── operation.sql
│   │   └── ...
│   ├── decorators
│   │   ├── transformers // 傳入值轉換用
│   │   │   ├── TransformToArray.ts
│   │   │   └── ...
│   │   └── validations // 傳入值驗證用
│   │   │   ├── IsNotBlank.ts
│   │       └── ...
│   ├── interceptors
│   │   └── response // 統一 API Response 格式
│   │       ├── response.interceptor.ts
│   │       └── response.interface.ts
│   ├── main.ts // 專案資源起始檔，配置API Prefix，API Server Port，Swagger Docs，logger (印 API request，response 資訊)...
│   ├── modules // 實作各個 API
│   │   ├── app-users
│   │   │   ├── app-users.controller.ts
│   │   │   ├── app-users.interface.ts
│   │   │   ├── app-users.module.ts
│   │   │   ├── app-users.service.ts
│   │   │   ├── dto // 定義 request 欄位，並且做欄位檢查
│   │   │   ├── entities // 定義 response 欄位
│   │   │   └── filter
│   │   │       ├── app-users-exception.filter.ts // 錯誤訊息轉換
│   │   │       ├── app-users-response.filter.ts // 回傳值轉換
│   │   │       └── app-users.filter.interface.ts // 定義本層錯誤訊息轉換，回傳值轉換的 interface
│   │   └── ...
│   ├── shared // call 外部 api
│   │   └── restful
│   │       ├── api // 通用
│   │       └── backend // 外部單位
│   │           └── users // 功能
│   └── utils // 全域共用方法
│       ├── swagger.ts // swagger 資料轉換
│       └── value-conversion.ts // 值的型別轉換
├── test // unit test
├── tsconfig.build.json
└── tsconfig.json
```
