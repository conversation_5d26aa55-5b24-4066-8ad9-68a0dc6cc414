# beanfun-nestjs-backstage-template

## Introduction

- Language: Typescript v5.4.3 (Node.js v20.18.2(npm v10.8.0))
- Framework: NestJs 10 ([Link](https://docs.nestjs.com/))
- Database: PostgreSQL
- Related Packages
  - [@nestjs/config](https://www.npmjs.com/package/@nestjs/config): Responsible for handling environment variables within the NestJS framework
  - [class-validator](https://www.npmjs.com/package/class-validator): Used for validating fields, ensuring data integrity
  - [class-transformer](https://www.npmjs.com/package/class-transformer): Facilitates field type transformation, optimizing data handling
  - [@nestjs/swagger](https://www.npmjs.com/package/@nestjs/swagger): Enables the generation of API documentation, enhancing API clarity and accessibility
  - [@okta/jwt-verifier](https://www.npmjs.com/package/@okta/jwt-verifier): Validates Okta access tokens issued by Okta authorization servers, ensuring secure access
  - [@nestjs/jwt](https://www.npmjs.com/package/@nestjs/jwt), [@nestjs/passport](https://www.npmjs.com/package/@nestjs/passport): Used for generating and verifying JWT tokens, ensuring secure authentication
  - [casbin](https://www.npmjs.com/package/casbin): Facilitates user permissions management within the application
  - [typeorm](https://www.npmjs.com/package/typeorm), [@nestjs/typeorm](https://www.npmjs.com/package/@nestjs/typeorm): Responsible for managing database operations within the NestJS framework
  - [axios](https://www.npmjs.com/package/axios), [@nestjs/axios](https://www.npmjs.com/package/@nestjs/axios): Enables the application to make HTTP requests to external service APIs
  - [dayjs](https://www.npmjs.com/package/dayjs): Used for efficient time conversion and manipulation within the application

## Create tables and import data

Step 1. Creating tables

```shell
cat src/database/*.sql | psql -h {database host} -d {database name} -f -
```

Step 2. Building the code (If the `casbin_rule` table does not exist, execute any of the `Step 4` commands to create it)

```shell
npm run build
```

Step 3. Copy the `.env.sample` file and rename it to `.env`. Adjust the following parameters

```
DB_TYPE=postgres
DB_HOST=(database host)
DB_PORT=(database port)
DB_USER=(database user name)
DB_PASSWORD=(database user password)
DB_NAME=(database name)
```

Step 4. Import data

The following command is retried up to 3 times

4-1. Import permission-related data and (if default roles exist, synchronously update the permissions of default roles)

```shell
npm run import:permission-data
```

4-2. Import operation-related data

```shell
npm run import:operation-data
```

4-3. Create admin role

```shell
npm run import:admin-role
```

4-4. Create admin user

```shell
npm run import:admin-user
```

## Notice

1. This project uses Husky

- In the `./husky/prepare-commit-msg` file, when committing, the commit message will automatically include the branch name.  
  For the detailed logic, please refer to `prepare-commit-msg`.

- In the `./husky/pre-push` file, automatically run `npm run build:dev` before pushing. If there are any issues, the push will be blocked.

## Development & testing

1. Refer to the `.env.sample` file to create a `.env` file

2. Start the Local API Server (debug mode)

   ```shell
   npm run start:debug
   ```

3. Open `http://localhost:3000/api/docs` to access Swagger for API testing

## Development note

1.  Naming convention

    - API endpoint naming: use pluralized nouns for resources

      ```
      Method: GET Path: /api/users (Get user list)
      Method: GET Path: /api/users/{userId} (Get user detail)
      Method: PUT/PATCH Path: /api/users/{userId} (PUT: Update user data / PATCH: Partially update user data)
      Method: DELETE Path: /api/users/{userId} (Delete user data)
      ```

    - variable naming: use lower camel case

    - table name and table field naming: use snake case

    - env: use upper snake case

2.  API requests should be defined in the `dto` folder, utilizing files specifically designated for them, and field validation should be implemented using the `class-validator` package

3.  API responses should be defined within the `entities` folder

4.  Using enums

    - Example: User status can be `UNVERIFIED`, `VERIFIED`, or `SUSPENDED`. Instead of using strings as types, use an enum

      ```ts
      export enum UserStatusEnum {
        UNVERIFIED = 'UNVERIFIED',
        VERIFIED = 'VERIFIED',
        SUSPENDED = 'SUSPENDED',
      }

      if (userStatus === UserStatusEnum.UNVERIFIED) { // O
        ...
      }

      if (userStatus === 'UNVERIFIED') { // X
        ....
      }
      ```

5.  Time fields

    - For table columns related to time, please use `bigint` for storage, and values should be unit timestamps (in milliseconds)

    - For API requests and responses, uniformly use the `number` data type

6.  All functions should explicitly define the interfaces for their input and return values. Whenever possible, avoid using the `any` type for fields in interfaces

    ```ts
    interface User {
      id: number;
      firstName: string;
      lastName: string;
      email: string;
      title?: number; // Optional
    }
    ```

7.  Some APIs require user permission checks

    - Development Steps

      Step 1. Add permission information in `permission-object-groups/permission-object-groups.interface.ts` (`permissionObjectGroup` represents functional categories, `permissionObject` represents functionalities, and `permissionAction` represents actions within functionalities)

      - Example: Retrieving a user list corresponds to [Auth Management > Role Management > Read]

        ```ts
        export namespace AUTHORIZATION_MANAGEMENT {
          export const permissionObjectGroupKey = 'AUTHORIZATION_MANAGEMENT';
          export const permissionObjectGroupName = '權限管理';
          export const permissionObjectGroupPosition = 1;

          export namespace ROLE_MANAGEMENT {
            export const permissionObjectKey = `${permissionObjectGroupKey}_ROLE_MANAGEMENT`;
            export const permissionObjectName = '角色管理';
            export const permissionObjectDescription = '角色設定';
            export const permissionObjectPosition = 1;

            export const READ = {
              permissionObjectKey,
              permissionActionKey: `${permissionObjectKey}_READ`,
              permissionActionName: '檢視',
              permissionActionPosition: 1,
              permissionActionDescription:
                '檢視角色，包含列表、單一角色詳細資料',
            };
          }
        }
        ```

      Step 2. Write a function in the `.service.ts` file to record operations

      Step 3. Call the function written in `Step 2` in the corresponding `.controller.ts` file

    - Note

      - When adding information in `permission-object-groups/permission-object-groups.interface.ts`, apart from being used in the code, it will also be written to the database during data import

      - By default, permission checks are required for every API endpoint. However, there are a few scenarios to note:

        - The `[GET] /api/apps-users` API used in app user management, xxx management does not require permission checks as it is a shared API

        - The `[GET] /api/permission-object-groups` API used in role management does not require permission checks as it is an auxiliary feature for role management

8.  Some APIs require recording operation logs

    - Development Steps

      Step 1. Add information in `operation-object-groups/operation-object-groups.interface.ts` (`operationObjectGroup` represents the functional category, `operationObject` represents the function, `operation` represents the action, and `operationEvent` represents the event)

      - Example 1: Get user list

        ```ts
        export namespace AUTHORIZATION_MANAGEMENT {
          export const operationObjectGroupKey = 'AUTHORIZATION_MANAGEMENT';
          export const operationObjectGroupName = '權限管理';
          export const operationObjectPosition = 1;
          export namespace USER_MANAGEMENT {
            export const operationObjectKey = `${operationObjectGroupKey}_USER_MANAGEMENT`;
            export const operationObjectName = '使用者設定';
            export const operationObjectPosition = 1;

            export const GET_USER_LIST = {
              operationObjectGroupKey,
              operationObjectKey,
              operationKey: `${operationObjectKey}_GET_USER_LIST`,
              operationName: '查詢使用者清單',
              operationPosition: 2,
              targetType: OperationLogTargetTypeEnum.USER,
            };
          }
        }
        ```

      - Example 2: Update user info

        ```ts
        export namespace AUTHORIZATION_MANAGEMENT {
          export const operationObjectGroupKey = 'AUTHORIZATION_MANAGEMENT';
          export const operationObjectGroupName = '權限管理';
          export const operationObjectPosition = 1;
          export namespace USER_MANAGEMENT {
            export const operationObjectKey = `${operationObjectGroupKey}_USER_MANAGEMENT`;
            export const operationObjectName = '使用者設定';
            export const operationObjectPosition = 1;
            export namespace UPDATE_USER {
              const targetType = OperationLogTargetTypeEnum.USER;
              export const operationKey = `${operationObjectKey}_UPDATE_USER`;
              export const operationName = '編輯使用者';
              export const operationPosition = 4;

              export const UPDATE_INFO = {
                operationObjectGroupKey,
                operationObjectKey,
                operationKey,
                operationEventKey: `${operationKey}_UPDATE_INFO`,
                operationEventName: '更改資訊',
                operationEventPosition: 1,
                targetType,
              };
              // MEMO: other update event
            }
          }
        }
        ```

      Step 2. Write the function to record operation logs in the `.service.ts` file

      Step 3. Call the function written in `Step 2` in the corresponding `.controller.ts` file

      - Example 1: For the `[GET] /api/roles` API, after querying the list in `roles.controller.ts`, the `createRoleOperationLog` function in `roles.service.ts` will be invoked to record the operation log in the database

      - Example 2: For the `[POST] /api/roles` API, after adding a role in `roles.controller.ts`, the `createRoleOperationLog` function in `roles.service.ts` will be invoked to record the operation log in the database

    - Note

      - In addition to being called in the code, when executing data import, relevant information will also be written to the database

      - The `target_type` is based on the resource

      - By default, every API endpoint requires logging of operations. However, there are a few scenarios to note:

        - The APIs used in app user management, xxx management, xxx management all utilize the `[GET] /api/app-users` API. Logging is only required when used in app user management; it's not necessary for the others

        - The `[GET] /api/permission-object-groups` API used in role management is a supplementary feature and does not require operation logging

9.  Install ESlint and Prettier. ESLint can check syntax, find problems, and enforce code style. Prettier will automatically format code. Please execute the command before creating a merge request

    ```shell
    npm run format
    ```

10. Before deployment, please pay attention to the following parameters to avoid security issues. Refer to [前後端資安風險](https://gamania-group.atlassian.net/wiki/spaces/BP/pages/*********):

- `APP_ENV`: When set to `prod`, Swagger will not be displayed, preventing the leakage of test documentation.
- `APP_CORS_ALLOW_ORIGIN`: Avoid setting it to `*`; it should specify the frontend domain.
- `APP_CORS_ALLOW_METHODS`: Avoid setting it to `*`; it should only include the methods used by the backend APIs.

11. Comments in the code type

    - `MEMO:`: Quick notes or reminders, like digital sticky notes, for keeping track of things to remember

    - `NOTE:`: A bit more urgent than `MEMO:`, highlighting important points that need attention

    - `TODO:`: Stuff that's on the to-do list, waiting to be tackled or improved

    - `SPEC:`: The nitty-gritty details of how things should work in terms of business logic

    - `FIXME:`: Problems or bugs that need fixing, ASAP

    - `PENDING:`: Stuff that's not quite ready yet, either in terms of implementation or release

12. Git Commit

    - Commit types

      - `feat`: Adding or changing features

      - `fix`: Fixing something that wasn't working right

      - `refactor`: Cleaning up the code, making it simpler or more organized

      - `perf`: Making things faster, like improving user experience or speeding up backend tasks

      - `test`: Adding, changing, or removing tests

      - `docs`: Updating comments or documentation

      - `style`: Making cosmetic changes, like formatting or spacing

      - `chore`: Miscellaneous changes, like setting up CI/CD

    - Commit format

      ```
      /^(revert: )?(feat|fix|refactor|perf|test|chore): .{1,50}/
      ```

    - Commit example

      ```
      feat: add chat room component
      ```

## Folder Structure

```
.
├── README.md
├── nest-cli.json
├── package-lock.json
├── package.json
├── src
│   ├── app.module.ts // import modules
│   ├── commands // import data into table
│   │   ├── console.ts
│   │   ├── import.console.ts
│   │   ├── import.module.ts
│   │   ├── import.service.ts
│   │   └── seeds
│   │       ├── admin-role.seed.ts
│   │       └── admin-user.seed.ts
│   ├── config // custom configuration files (https://docs.nestjs.com/techniques/configuration)
│   │   ├── auth.config.ts
│   │   └── ...
│   ├── database // related to database operations (1).sql: create table statement (2).ts: using TypeScript to define table columns
│   │   ├── operation.entity.ts
│   │   ├── operation.sql
│   │   └── ...
│   ├── decorators
│   │   ├── transformers // value transformers
│   │   │   ├── TransformToArray.ts
│   │   │   └── ...
│   │   └── validations // value validations
│   │       ├── IsNotBlank.ts
│   │       └── ...
│   ├── interceptors
│   │   └── response // standardizing API Response
│   │       ├── response.interceptor.ts
│   │       └── response.interface.ts
│   ├── main.ts // the entry file of the application( configures API prefix, API server port, Swagger Docs...)
│   ├── modules // implementations of various APIs
│   │   ├── app-users
│   │   │   ├── app-users.controller.ts
│   │   │   ├── app-users.interface.ts
│   │   │   ├── app-users.module.ts
│   │   │   ├── app-users.service.ts
│   │   │   ├── dto // defines request fields and performs field validation
│   │   │   ├── entities // Defines response fields
│   │   │   └── filter
│   │   │       ├── app-users-exception.filter.ts // error message conversion
│   │   │       ├── app-users-response.filter.ts // response value conversion
│   │   │       └── app-users.filter.interface.ts // defines interfaces for error message conversion and response value conversion at this layer
│   │   └── ...
│   ├── shared // call external APIs
│   │   └── restful
│   │       ├── api
│   │       └── backend
│   │           └── users
│   └── utils // global shared methods
│       ├── swagger.ts // swagger data conversion
│       └── value-conversion.ts // value type conversion
├── test // Unit tests
├── tsconfig.build.json
└── tsconfig.json
```
